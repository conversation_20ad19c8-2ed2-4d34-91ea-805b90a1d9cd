// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  USER
  ADMIN
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum PaymentMethod {
  STRIPE
  PAYPAL
  MTN_MOBILE_MONEY
  ORANGE_MOBILE_MONEY
}

model User {
  id          String    @id @default(cuid())
  email       String    @unique
  password    String
  firstName   String
  lastName    String
  phone       String?
  dateOfBirth DateTime?
  role        UserRole  @default(USER)
  isActive    Boolean   @default(true)
  avatar      String?
  preferences Json? // Store user preferences for recommendations
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  bookings             Booking[]
  payments             Payment[]
  reviews              Review[]
  favoriteDestinations FavoriteDestination[]
  eventRegistrations   EventRegistration[]

  @@map("users")
}

model Destination {
  id               String   @id @default(cuid())
  name             String
  description      String
  shortDescription String?
  location         String
  latitude         Float?
  longitude        Float?
  category         String
  images           String[] // Array of image URLs
  priceFrom        Int // Price in CFA francs
  duration         String // e.g., "3 days", "1 week"
  difficulty       String? // e.g., "Easy", "Moderate", "Challenging"
  highlights       String[] // Array of highlights
  included         String[] // What's included in the package
  excluded         String[] // What's not included
  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  bookings             Booking[]
  reviews              Review[]
  favoriteDestinations FavoriteDestination[]
  packages             Package[]

  @@map("destinations")
}

model Hotel {
  id            String   @id @default(cuid())
  name          String
  description   String
  location      String
  latitude      Float?
  longitude     Float?
  address       String
  phone         String?
  email         String?
  website       String?
  images        String[] // Array of image URLs
  amenities     String[] // Array of amenities
  rating        Float?   @default(0)
  pricePerNight Int // Price in CFA francs
  totalRooms    Int
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  rooms    Room[]
  bookings Booking[]
  reviews  Review[]

  @@map("hotels")
}

model Room {
  id            String   @id @default(cuid())
  hotelId       String
  name          String
  description   String?
  type          String // e.g., "Single", "Double", "Suite"
  capacity      Int
  pricePerNight Int // Price in CFA francs
  images        String[] // Array of image URLs
  amenities     String[] // Array of room amenities
  isAvailable   Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  hotel    Hotel     @relation(fields: [hotelId], references: [id], onDelete: Cascade)
  bookings Booking[]

  @@map("rooms")
}

model Event {
  id               String   @id @default(cuid())
  name             String
  description      String
  shortDescription String?
  location         String
  latitude         Float?
  longitude        Float?
  startDate        DateTime
  endDate          DateTime
  category         String // e.g., "Cultural", "Music", "Sports", "Festival"
  images           String[] // Array of image URLs
  price            Int // Price in CFA francs
  maxAttendees     Int?
  currentAttendees Int      @default(0)
  highlights       String[] // Array of event highlights
  requirements     String[] // Requirements for attendees
  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  registrations EventRegistration[]
  reviews       Review[]

  @@map("events")
}

model Package {
  id            String   @id @default(cuid())
  name          String
  description   String
  destinationId String
  duration      String // e.g., "5 days 4 nights"
  price         Int // Price in CFA francs
  images        String[] // Array of image URLs
  included      String[] // What's included
  excluded      String[] // What's not included
  itinerary     Json // Detailed day-by-day itinerary
  maxPeople     Int?
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  destination Destination @relation(fields: [destinationId], references: [id], onDelete: Cascade)
  bookings    Booking[]

  @@map("packages")
}

model Booking {
  id              String        @id @default(cuid())
  userId          String
  destinationId   String?
  hotelId         String?
  roomId          String?
  packageId       String?
  checkIn         DateTime?
  checkOut        DateTime?
  guests          Int           @default(1)
  totalAmount     Int // Total amount in CFA francs
  status          BookingStatus @default(PENDING)
  specialRequests String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  destination Destination? @relation(fields: [destinationId], references: [id])
  hotel       Hotel?       @relation(fields: [hotelId], references: [id])
  room        Room?        @relation(fields: [roomId], references: [id])
  package     Package?     @relation(fields: [packageId], references: [id])
  payments    Payment[]

  @@map("bookings")
}

model EventRegistration {
  id          String        @id @default(cuid())
  userId      String
  eventId     String
  attendees   Int           @default(1)
  totalAmount Int // Total amount in CFA francs
  status      BookingStatus @default(PENDING)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  event    Event     @relation(fields: [eventId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@unique([userId, eventId])
  @@map("event_registrations")
}

model Payment {
  id                  String        @id @default(cuid())
  userId              String
  bookingId           String?
  eventRegistrationId String?
  amount              Int // Amount in CFA francs
  currency            String        @default("XAF") // Central African CFA franc
  method              PaymentMethod
  status              PaymentStatus @default(PENDING)
  transactionId       String? // External payment gateway transaction ID
  metadata            Json? // Additional payment metadata
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt

  // Relations
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  booking           Booking?           @relation(fields: [bookingId], references: [id])
  eventRegistration EventRegistration? @relation(fields: [eventRegistrationId], references: [id])

  @@map("payments")
}

model Review {
  id            String   @id @default(cuid())
  userId        String
  destinationId String?
  hotelId       String?
  eventId       String?
  rating        Int // 1-5 stars
  title         String?
  comment       String
  images        String[] // Array of review image URLs
  isVerified    Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  destination Destination? @relation(fields: [destinationId], references: [id])
  hotel       Hotel?       @relation(fields: [hotelId], references: [id])
  event       Event?       @relation(fields: [eventId], references: [id])

  @@map("reviews")
}

model FavoriteDestination {
  id            String   @id @default(cuid())
  userId        String
  destinationId String
  createdAt     DateTime @default(now())

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  destination Destination @relation(fields: [destinationId], references: [id], onDelete: Cascade)

  @@unique([userId, destinationId])
  @@map("favorite_destinations")
}
