import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, MapPin, Clock, Users, Calendar, Star, Share2 } from 'lucide-react';
import ImageGallery from 'react-image-gallery';
import 'react-image-gallery/styles/css/image-gallery.css';
import { destinationsData } from '../data/destinations';
import { destinationsAPI } from '../services/api';
import PaymentSection from '../components/PaymentSection';
import TourBookingForm from '../components/TourBookingForm';
import RecommendationSection from '../components/RecommendationSection';
import toast from 'react-hot-toast';

const DestinationDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [destination, setDestination] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch destination data from API
  useEffect(() => {
    const fetchDestination = async () => {
      if (!id) {
        setError('No destination ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await destinationsAPI.getDestinationById(id);
        setDestination(response.data.data.destination);
        setError(null);
      } catch (err: any) {
        console.error('Error fetching destination:', err);
        // Fallback to static data if API fails
        const numId = parseInt(id, 10);
        const fallbackDestination = destinationsData.find(dest => dest.id === numId);
        if (fallbackDestination) {
          setDestination(fallbackDestination);
          setError(null);
        } else {
          setError('Destination not found');
          toast.error('Failed to load destination details');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchDestination();
  }, [id]);
  
  // Loading state
  if (loading) {
    return (
      <div className="pt-16 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="container mx-auto px-4 py-20 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading destination details...</p>
        </div>
      </div>
    );
  }

  // Error state or destination not found
  if (error || !destination) {
    return (
      <div className="pt-16 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="container mx-auto px-4 py-20 text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {error || 'Destination not found'}
          </h2>
          <button
            className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            onClick={() => navigate('/destinations')}
          >
            <ArrowLeft className="mr-2" size={16} />
            Back to destinations
          </button>
        </div>
      </div>
    );
  }
  
  // Format images for gallery
  const images = destination.images && destination.images.length > 0
    ? destination.images.map((img: string) => ({
        original: img,
        thumbnail: img
      }))
    : destination.image
      ? [
          { original: destination.image, thumbnail: destination.image },
          // Add some fallback images for better gallery experience
          { original: 'https://images.pexels.com/photos/2440079/pexels-photo-2440079.jpeg?auto=compress&cs=tinysrgb&w=500&h=300&dpr=2', thumbnail: 'https://images.pexels.com/photos/2440079/pexels-photo-2440079.jpeg?auto=compress&cs=tinysrgb&w=100&h=80&dpr=2' },
          { original: 'https://images.pexels.com/photos/2325446/pexels-photo-2325446.jpeg?auto=compress&cs=tinysrgb&w=500&h=300&dpr=2', thumbnail: 'https://images.pexels.com/photos/2325446/pexels-photo-2325446.jpeg?auto=compress&cs=tinysrgb&w=100&h=80&dpr=2' },
        ]
      : [
          { original: 'https://images.pexels.com/photos/417074/pexels-photo-417074.jpeg?auto=compress&cs=tinysrgb&w=500&h=300&dpr=2', thumbnail: 'https://images.pexels.com/photos/417074/pexels-photo-417074.jpeg?auto=compress&cs=tinysrgb&w=100&h=80&dpr=2' }
        ];

  return (
    <div className="pt-16 bg-gray-50 dark:bg-gray-900">
      {/* Back button */}
      <div className="container mx-auto px-4 py-4">
        <button 
          className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg"
          onClick={() => navigate('/destinations')}
        >
          <ArrowLeft className="mr-1" size={16} />
          Back
        </button>
      </div>
      
      <div className="container mx-auto px-4 pb-20">
        {/* Top section with name and basic info */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">{destination.name}</h1>
          <div className="flex items-center mt-2 text-gray-600 dark:text-gray-400">
            <MapPin size={18} className="mr-1" />
            <span className="mr-4">{destination.location}</span>
            <Star size={18} className="mr-1 text-yellow-500" />
            <span className="mr-4">{destination.averageRating || destination.rating || 4.5} ({destination.totalReviews || 120} reviews)</span>
          </div>
        </div>
        
        {/* Image Gallery */}
        <div className="mb-12 rounded-xl overflow-hidden">
          <ImageGallery 
            items={images} 
            showPlayButton={false}
            showFullscreenButton={true}
            showThumbnails={true}
            showBullets={true}
          />
        </div>
        
        {/* Content in two columns on larger screens */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {/* Description */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm mb-8">
              <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">About {destination.name}</h2>
              <p className="text-gray-700 dark:text-gray-300 mb-4">
                {destination.description}
              </p>
              {destination.shortDescription && (
                <p className="text-gray-700 dark:text-gray-300 mb-4 font-medium">
                  {destination.shortDescription}
                </p>
              )}

              {/* Highlights section */}
              {destination.highlights && destination.highlights.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Highlights</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300">
                    {destination.highlights.map((highlight: string, index: number) => (
                      <li key={index}>{highlight}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {/* Key information */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-8">
                <div className="flex items-center">
                  <Clock className="text-blue-600 mr-2" size={20} />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Duration</p>
                    <p className="font-medium text-gray-900 dark:text-white">{destination.duration}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Users className="text-blue-600 mr-2" size={20} />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Difficulty</p>
                    <p className="font-medium text-gray-900 dark:text-white">{destination.difficulty || destination.groupSize || 'Moderate'}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Calendar className="text-blue-600 mr-2" size={20} />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Best time to visit</p>
                    <p className="font-medium text-gray-900 dark:text-white">Nov - Feb</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Map section */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm mb-8">
              <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Location</h2>
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 overflow-hidden">
                {/* Map will be implemented with MapboxGL */}
                <div className="h-full w-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                  Interactive map will be displayed here
                </div>
              </div>
              <p className="text-gray-700 dark:text-gray-300">
                {destination.location}, Cameroon
              </p>
            </div>
            
            {/* More content sections like reviews could go here */}
          </div>
          
          {/* Sidebar */}
          <div>
            {/* Booking form */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm mb-8 sticky top-24">
              <div className="mb-4 flex justify-between items-center">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Book This Tour</h3>
                <span className="text-xl font-bold text-blue-600">
                  {destination.priceFrom ? `${destination.priceFrom.toLocaleString()} CFA` : destination.price}
                </span>
              </div>
              
              <TourBookingForm destination={destination} />
            </div>
          </div>
        </div>
        
        {/* Recommendations */}
        <RecommendationSection currentId={destination.id} />
      </div>
    </div>
  );
};

export default DestinationDetail;
