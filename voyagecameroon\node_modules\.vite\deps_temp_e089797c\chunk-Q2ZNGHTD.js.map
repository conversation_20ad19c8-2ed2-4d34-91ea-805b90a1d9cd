{"version": 3, "sources": ["../../@firebase/util/dist/postinstall.mjs", "../../@firebase/util/src/constants.ts", "../../@firebase/util/src/assert.ts", "../../@firebase/util/src/crypt.ts", "../../@firebase/util/src/deepCopy.ts", "../../@firebase/util/src/global.ts", "../../@firebase/util/src/defaults.ts", "../../@firebase/util/src/deferred.ts", "../../@firebase/util/src/url.ts", "../../@firebase/util/src/emulator.ts", "../../@firebase/util/src/environment.ts", "../../@firebase/util/src/errors.ts", "../../@firebase/util/src/json.ts", "../../@firebase/util/src/jwt.ts", "../../@firebase/util/src/obj.ts", "../../@firebase/util/src/promise.ts", "../../@firebase/util/src/query.ts", "../../@firebase/util/src/sha1.ts", "../../@firebase/util/src/subscribe.ts", "../../@firebase/util/src/validation.ts", "../../@firebase/util/src/utf8.ts", "../../@firebase/util/src/exponential_backoff.ts", "../../@firebase/util/src/formatters.ts", "../../@firebase/util/src/compat.ts", "../../@firebase/component/src/component.ts", "../../@firebase/component/src/constants.ts", "../../@firebase/component/src/provider.ts", "../../@firebase/component/src/component_container.ts", "../../@firebase/logger/src/logger.ts", "../../idb/build/wrap-idb-value.js", "../../idb/build/index.js", "../../@firebase/app/src/platformLoggerService.ts", "../../@firebase/app/src/logger.ts", "../../@firebase/app/src/constants.ts", "../../@firebase/app/src/internal.ts", "../../@firebase/app/src/errors.ts", "../../@firebase/app/src/firebaseApp.ts", "../../@firebase/app/src/firebaseServerApp.ts", "../../@firebase/app/src/api.ts", "../../@firebase/app/src/indexeddb.ts", "../../@firebase/app/src/heartbeatService.ts", "../../@firebase/app/src/registerCoreComponents.ts", "../../@firebase/app/src/index.ts"], "sourcesContent": ["const getDefaultsFromPostinstall = () => (undefined);\nexport { getDefaultsFromPostinstall };", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\n */\n\nexport const CONSTANTS = {\n  /**\n   * @define {boolean} Whether this is the client Node.js SDK.\n   */\n  NODE_CLIENT: false,\n  /**\n   * @define {boolean} Whether this is the Admin Node.js SDK.\n   */\n  NODE_ADMIN: false,\n\n  /**\n   * Firebase SDK Version\n   */\n  SDK_VERSION: '${JSCORE_VERSION}'\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\n\n/**\n * Throws an error if the provided assertion is falsy\n */\nexport const assert = function (assertion: unknown, message: string): void {\n  if (!assertion) {\n    throw assertionError(message);\n  }\n};\n\n/**\n * Returns an Error object suitable for throwing.\n */\nexport const assertionError = function (message: string): Error {\n  return new Error(\n    'Firebase Database (' +\n      CONSTANTS.SDK_VERSION +\n      ') INTERNAL ASSERT FAILED: ' +\n      message\n  );\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst stringToByteArray = function (str: string): number[] {\n  // TODO(user): Use native implementations if/when available\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (\n      (c & 0xfc00) === 0xd800 &&\n      i + 1 < str.length &&\n      (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes: number[]): string {\n  // TODO(user): Use native implementations if/when available\n  const out: string[] = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u =\n        (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n        0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode(\n        ((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)\n      );\n    }\n  }\n  return out.join('');\n};\n\ninterface Base64 {\n  byteToCharMap_: { [key: number]: string } | null;\n  charToByteMap_: { [key: string]: number } | null;\n  byteToCharMapWebSafe_: { [key: number]: string } | null;\n  charToByteMapWebSafe_: { [key: string]: number } | null;\n  ENCODED_VALS_BASE: string;\n  readonly ENCODED_VALS: string;\n  readonly ENCODED_VALS_WEBSAFE: string;\n  HAS_NATIVE_SUPPORT: boolean;\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string;\n  encodeString(input: string, webSafe?: boolean): string;\n  decodeString(input: string, webSafe: boolean): string;\n  decodeStringToByteArray(input: string, webSafe: boolean): number[];\n  init_(): void;\n}\n\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nexport const base64: Base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE:\n    'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input: number[] | Uint8Array, webSafe?: boolean): string {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n\n    this.init_();\n\n    const byteToCharMap = webSafe\n      ? this.byteToCharMapWebSafe_!\n      : this.byteToCharMap_!;\n\n    const output = [];\n\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n\n      const outByte1 = byte1 >> 2;\n      const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n      let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n      let outByte4 = byte3 & 0x3f;\n\n      if (!haveByte3) {\n        outByte4 = 64;\n\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n\n      output.push(\n        byteToCharMap[outByte1],\n        byteToCharMap[outByte2],\n        byteToCharMap[outByte3],\n        byteToCharMap[outByte4]\n      );\n    }\n\n    return output.join('');\n  },\n\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input: string, webSafe?: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray(input), webSafe);\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input: string, webSafe: boolean): string {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input: string, webSafe: boolean): number[] {\n    this.init_();\n\n    const charToByteMap = webSafe\n      ? this.charToByteMapWebSafe_!\n      : this.charToByteMap_!;\n\n    const output: number[] = [];\n\n    for (let i = 0; i < input.length; ) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n\n      const outByte1 = (byte1 << 2) | (byte2 >> 4);\n      output.push(outByte1);\n\n      if (byte3 !== 64) {\n        const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n        output.push(outByte2);\n\n        if (byte4 !== 64) {\n          const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n\n    return output;\n  },\n\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n\n/**\n * An error encountered while decoding base64 string.\n */\nexport class DecodeBase64StringError extends Error {\n  readonly name = 'DecodeBase64StringError';\n}\n\n/**\n * URL-safe base64 encoding\n */\nexport const base64Encode = function (str: string): string {\n  const utf8Bytes = stringToByteArray(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nexport const base64urlEncodeWithoutPadding = function (str: string): string {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nexport const base64Decode = function (str: string): string | null {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nexport function deepCopy<T>(value: T): T {\n  return deepExtend(undefined, value) as T;\n}\n\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nexport function deepExtend(target: unknown, source: unknown): unknown {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source as Date;\n      return new Date(dateValue.getTime());\n\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    (target as Record<string, unknown>)[prop] = deepExtend(\n      (target as Record<string, unknown>)[prop],\n      (source as Record<string, unknown>)[prop]\n    );\n  }\n\n  return target;\n}\n\nfunction isValidKey(key: string): boolean {\n  return key !== '__proto__';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nexport function getGlobal(): typeof globalThis {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { getGlobal } from './global';\nimport { getDefaultsFromPostinstall } from './postinstall';\n\n/**\n * Keys for experimental properties on the `FirebaseDefaults` object.\n * @public\n */\nexport type ExperimentalKey = 'authTokenSyncURL' | 'authIdTokenMaxAge';\n\n/**\n * An object that can be injected into the environment as __FIREBASE_DEFAULTS__,\n * either as a property of globalThis, a shell environment variable, or a\n * cookie.\n *\n * This object can be used to automatically configure and initialize\n * a Firebase app as well as any emulators.\n *\n * @public\n */\nexport interface FirebaseDefaults {\n  config?: Record<string, string>;\n  emulatorHosts?: Record<string, string>;\n  _authTokenSyncURL?: string;\n  _authIdTokenMaxAge?: number;\n  /**\n   * Override Firebase's runtime environment detection and\n   * force the SDK to act as if it were in the specified environment.\n   */\n  forceEnvironment?: 'browser' | 'node';\n  [key: string]: unknown;\n}\n\ndeclare global {\n  // Need `var` for this to work.\n  // eslint-disable-next-line no-var\n  var __FIREBASE_DEFAULTS__: FirebaseDefaults | undefined;\n}\n\nconst getDefaultsFromGlobal = (): FirebaseDefaults | undefined =>\n  getGlobal().__FIREBASE_DEFAULTS__;\n\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = (): FirebaseDefaults | undefined => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\n\nconst getDefaultsFromCookie = (): FirebaseDefaults | undefined => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nexport const getDefaults = (): FirebaseDefaults | undefined => {\n  try {\n    return (\n      getDefaultsFromPostinstall() ||\n      getDefaultsFromGlobal() ||\n      getDefaultsFromEnvVariable() ||\n      getDefaultsFromCookie()\n    );\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nexport const getDefaultEmulatorHost = (\n  productName: string\n): string | undefined => getDefaults()?.emulatorHosts?.[productName];\n\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nexport const getDefaultEmulatorHostnameAndPort = (\n  productName: string\n): [hostname: string, port: number] | undefined => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nexport const getDefaultAppConfig = (): Record<string, string> | undefined =>\n  getDefaults()?.config;\n\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nexport const getExperimentalSetting = <T extends ExperimentalKey>(\n  name: T\n): FirebaseDefaults[`_${T}`] =>\n  getDefaults()?.[`_${name}`] as FirebaseDefaults[`_${T}`];\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport class Deferred<R> {\n  promise: Promise<R>;\n  reject: (value?: unknown) => void = () => {};\n  resolve: (value?: unknown) => void = () => {};\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve as (value?: unknown) => void;\n      this.reject = reject as (value?: unknown) => void;\n    });\n  }\n\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(\n    callback?: (error?: unknown, value?: unknown) => void\n  ): (error: unknown, value?: unknown) => void {\n    return (error, value?) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nexport function isCloudWorkstation(url: string): boolean {\n  // `isCloudWorkstation` is called without protocol in certain connect*Emulator functions\n  // In HTTP request builders, it's called with the protocol.\n  // If called with protocol prefix, it's a valid URL, so we extract the hostname\n  // If called without, we assume the string is the hostname.\n  try {\n    const host =\n      url.startsWith('http://') || url.startsWith('https://')\n        ? new URL(url).hostname\n        : url;\n    return host.endsWith('.cloudworkstations.dev');\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nexport async function pingServer(endpoint: string): Promise<boolean> {\n  const result = await fetch(endpoint, {\n    credentials: 'include'\n  });\n  return result.ok;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64urlEncodeWithoutPadding } from './crypt';\nimport { isCloudWorkstation } from './url';\n\n// Firebase Auth tokens contain snake_case claims following the JWT standard / convention.\n/* eslint-disable camelcase */\n\nexport type FirebaseSignInProvider =\n  | 'custom'\n  | 'email'\n  | 'password'\n  | 'phone'\n  | 'anonymous'\n  | 'google.com'\n  | 'facebook.com'\n  | 'github.com'\n  | 'twitter.com'\n  | 'microsoft.com'\n  | 'apple.com';\n\ninterface FirebaseIdToken {\n  // Always set to https://securetoken.google.com/PROJECT_ID\n  iss: string;\n\n  // Always set to PROJECT_ID\n  aud: string;\n\n  // The user's unique ID\n  sub: string;\n\n  // The token issue time, in seconds since epoch\n  iat: number;\n\n  // The token expiry time, normally 'iat' + 3600\n  exp: number;\n\n  // The user's unique ID. Must be equal to 'sub'\n  user_id: string;\n\n  // The time the user authenticated, normally 'iat'\n  auth_time: number;\n\n  // The sign in provider, only set when the provider is 'anonymous'\n  provider_id?: 'anonymous';\n\n  // The user's primary email\n  email?: string;\n\n  // The user's email verification status\n  email_verified?: boolean;\n\n  // The user's primary phone number\n  phone_number?: string;\n\n  // The user's display name\n  name?: string;\n\n  // The user's profile photo URL\n  picture?: string;\n\n  // Information on all identities linked to this user\n  firebase: {\n    // The primary sign-in provider\n    sign_in_provider: FirebaseSignInProvider;\n\n    // A map of providers to the user's list of unique identifiers from\n    // each provider\n    identities?: { [provider in FirebaseSignInProvider]?: string[] };\n  };\n\n  // Custom claims set by the developer\n  [claim: string]: unknown;\n\n  uid?: never; // Try to catch a common mistake of \"uid\" (should be \"sub\" instead).\n}\n\nexport type EmulatorMockTokenOptions = ({ user_id: string } | { sub: string }) &\n  Partial<FirebaseIdToken>;\n\nexport function createMockUserToken(\n  token: EmulatorMockTokenOptions,\n  projectId?: string\n): string {\n  if (token.uid) {\n    throw new Error(\n      'The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.'\n    );\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n\n  const payload: FirebaseIdToken = {\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    },\n\n    // Override with user options\n    ...token\n  };\n\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [\n    base64urlEncodeWithoutPadding(JSON.stringify(header)),\n    base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n    signature\n  ].join('.');\n}\n\ninterface EmulatorStatusMap {\n  [name: string]: boolean;\n}\nconst emulatorStatus: EmulatorStatusMap = {};\n\ninterface EmulatorSummary {\n  prod: string[];\n  emulator: string[];\n}\n\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary(): EmulatorSummary {\n  const summary: EmulatorSummary = {\n    prod: [],\n    emulator: []\n  };\n  for (const key of Object.keys(emulatorStatus)) {\n    if (emulatorStatus[key]) {\n      summary.emulator.push(key);\n    } else {\n      summary.prod.push(key);\n    }\n  }\n  return summary;\n}\n\nfunction getOrCreateEl(id: string): { created: boolean; element: HTMLElement } {\n  let parentDiv = document.getElementById(id);\n  let created = false;\n  if (!parentDiv) {\n    parentDiv = document.createElement('div');\n    parentDiv.setAttribute('id', id);\n    created = true;\n  }\n  return { created, element: parentDiv };\n}\n\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nexport function updateEmulatorBanner(\n  name: string,\n  isRunningEmulator: boolean\n): void {\n  if (\n    typeof window === 'undefined' ||\n    typeof document === 'undefined' ||\n    !isCloudWorkstation(window.location.host) ||\n    emulatorStatus[name] === isRunningEmulator ||\n    emulatorStatus[name] || // If already set to use emulator, can't go back to prod.\n    previouslyDismissed\n  ) {\n    return;\n  }\n\n  emulatorStatus[name] = isRunningEmulator;\n\n  function prefixedId(id: string): string {\n    return `__firebase__banner__${id}`;\n  }\n  const bannerId = '__firebase__banner';\n  const summary = getEmulatorSummary();\n  const showError = summary.prod.length > 0;\n\n  function tearDown(): void {\n    const element = document.getElementById(bannerId);\n    if (element) {\n      element.remove();\n    }\n  }\n\n  function setupBannerStyles(bannerEl: HTMLElement): void {\n    bannerEl.style.display = 'flex';\n    bannerEl.style.background = '#7faaf0';\n    bannerEl.style.position = 'fixed';\n    bannerEl.style.bottom = '5px';\n    bannerEl.style.left = '5px';\n    bannerEl.style.padding = '.5em';\n    bannerEl.style.borderRadius = '5px';\n    bannerEl.style.alignItems = 'center';\n  }\n\n  function setupIconStyles(prependIcon: SVGElement, iconId: string): void {\n    prependIcon.setAttribute('width', '24');\n    prependIcon.setAttribute('id', iconId);\n    prependIcon.setAttribute('height', '24');\n    prependIcon.setAttribute('viewBox', '0 0 24 24');\n    prependIcon.setAttribute('fill', 'none');\n    prependIcon.style.marginLeft = '-6px';\n  }\n\n  function setupCloseBtn(): HTMLSpanElement {\n    const closeBtn = document.createElement('span');\n    closeBtn.style.cursor = 'pointer';\n    closeBtn.style.marginLeft = '16px';\n    closeBtn.style.fontSize = '24px';\n    closeBtn.innerHTML = ' &times;';\n    closeBtn.onclick = () => {\n      previouslyDismissed = true;\n      tearDown();\n    };\n    return closeBtn;\n  }\n\n  function setupLinkStyles(\n    learnMoreLink: HTMLAnchorElement,\n    learnMoreId: string\n  ): void {\n    learnMoreLink.setAttribute('id', learnMoreId);\n    learnMoreLink.innerText = 'Learn more';\n    learnMoreLink.href =\n      'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n    learnMoreLink.setAttribute('target', '__blank');\n    learnMoreLink.style.paddingLeft = '5px';\n    learnMoreLink.style.textDecoration = 'underline';\n  }\n\n  function setupDom(): void {\n    const banner = getOrCreateEl(bannerId);\n    const firebaseTextId = prefixedId('text');\n    const firebaseText: HTMLSpanElement =\n      document.getElementById(firebaseTextId) || document.createElement('span');\n    const learnMoreId = prefixedId('learnmore');\n    const learnMoreLink: HTMLAnchorElement =\n      (document.getElementById(learnMoreId) as HTMLAnchorElement) ||\n      document.createElement('a');\n    const prependIconId = prefixedId('preprendIcon');\n    const prependIcon: SVGElement =\n      (document.getElementById(\n        prependIconId\n      ) as HTMLOrSVGElement as SVGElement) ||\n      document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    if (banner.created) {\n      // update styles\n      const bannerEl = banner.element;\n      setupBannerStyles(bannerEl);\n      setupLinkStyles(learnMoreLink, learnMoreId);\n      const closeBtn = setupCloseBtn();\n      setupIconStyles(prependIcon, prependIconId);\n      bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n      document.body.appendChild(bannerEl);\n    }\n\n    if (showError) {\n      firebaseText.innerText = `Preview backend disconnected.`;\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n    } else {\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n      firebaseText.innerText = 'Preview backend running in this workspace.';\n    }\n    firebaseText.setAttribute('id', firebaseTextId);\n  }\n  if (document.readyState === 'loading') {\n    window.addEventListener('DOMContentLoaded', setupDom);\n  } else {\n    setupDom();\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CONSTANTS } from './constants';\nimport { getDefaults } from './defaults';\n\n/**\n * Type placeholder for `WorkerGlobalScope` from `webworker`\n */\ndeclare class WorkerGlobalScope {}\n\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nexport function getUA(): string {\n  if (\n    typeof navigator !== 'undefined' &&\n    typeof navigator['userAgent'] === 'string'\n  ) {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nexport function isMobileCordova(): boolean {\n  return (\n    typeof window !== 'undefined' &&\n    // @ts-ignore Setting up an broadly applicable index signature for Window\n    // just to deal with this case would probably be a bad idea.\n    !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n    /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())\n  );\n}\n\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nexport function isNode(): boolean {\n  const forceEnvironment = getDefaults()?.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n\n  try {\n    return (\n      Object.prototype.toString.call(global.process) === '[object process]'\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n\n/**\n * Detect Web Worker context.\n */\nexport function isWebWorker(): boolean {\n  return (\n    typeof WorkerGlobalScope !== 'undefined' &&\n    typeof self !== 'undefined' &&\n    self instanceof WorkerGlobalScope\n  );\n}\n\n/**\n * Detect Cloudflare Worker context.\n */\nexport function isCloudflareWorker(): boolean {\n  return (\n    typeof navigator !== 'undefined' &&\n    navigator.userAgent === 'Cloudflare-Workers'\n  );\n}\n\n/**\n * Detect browser extensions (Chrome and Firefox at least).\n */\ninterface BrowserRuntime {\n  id?: unknown;\n}\ndeclare const chrome: { runtime?: BrowserRuntime };\ndeclare const browser: { runtime?: BrowserRuntime };\nexport function isBrowserExtension(): boolean {\n  const runtime =\n    typeof chrome === 'object'\n      ? chrome.runtime\n      : typeof browser === 'object'\n      ? browser.runtime\n      : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nexport function isReactNative(): boolean {\n  return (\n    typeof navigator === 'object' && navigator['product'] === 'ReactNative'\n  );\n}\n\n/** Detects Electron apps. */\nexport function isElectron(): boolean {\n  return getUA().indexOf('Electron/') >= 0;\n}\n\n/** Detects Internet Explorer. */\nexport function isIE(): boolean {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n\n/** Detects Universal Windows Platform apps. */\nexport function isUWP(): boolean {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nexport function isNodeSdk(): boolean {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n\n/** Returns true if we are running in Safari. */\nexport function isSafari(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    navigator.userAgent.includes('Safari') &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/** Returns true if we are running in Safari or WebKit */\nexport function isSafariOrWebkit(): boolean {\n  return (\n    !isNode() &&\n    !!navigator.userAgent &&\n    (navigator.userAgent.includes('Safari') ||\n      navigator.userAgent.includes('WebKit')) &&\n    !navigator.userAgent.includes('Chrome')\n  );\n}\n\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nexport function isIndexedDBAvailable(): boolean {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nexport function validateIndexedDBOpenable(): Promise<boolean> {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist: boolean = true;\n      const DB_CHECK_NAME =\n        'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n\n      request.onerror = () => {\n        reject(request.error?.message || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nexport function areCookiesEnabled(): boolean {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Evaluates a JSON string into a javascript object.\n *\n * @param {string} str A string containing JSON.\n * @return {*} The javascript object representing the specified JSON.\n */\nexport function jsonEval(str: string): unknown {\n  return JSON.parse(str);\n}\n\n/**\n * Returns JSON representing a javascript object.\n * @param {*} data JavaScript object to be stringified.\n * @return {string} The JSON contents of the object.\n */\nexport function stringify(data: unknown): string {\n  return JSON.stringify(data);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { base64Decode } from './crypt';\nimport { jsonEval } from './json';\n\ninterface Claims {\n  [key: string]: {};\n}\n\ninterface DecodedToken {\n  header: object;\n  claims: Claims;\n  data: object;\n  signature: string;\n}\n\n/**\n * Decodes a Firebase auth. token into constituent parts.\n *\n * Notes:\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const decode = function (token: string): DecodedToken {\n  let header = {},\n    claims: Claims = {},\n    data = {},\n    signature = '';\n\n  try {\n    const parts = token.split('.');\n    header = jsonEval(base64Decode(parts[0]) || '') as object;\n    claims = jsonEval(base64Decode(parts[1]) || '') as Claims;\n    signature = parts[2];\n    data = claims['d'] || {};\n    delete claims['d'];\n  } catch (e) {}\n\n  return {\n    header,\n    claims,\n    data,\n    signature\n  };\n};\n\ninterface DecodedToken {\n  header: object;\n  claims: Claims;\n  data: object;\n  signature: string;\n}\n\n/**\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isValidTimestamp = function (token: string): boolean {\n  const claims: Claims = decode(token).claims;\n  const now: number = Math.floor(new Date().getTime() / 1000);\n  let validSince: number = 0,\n    validUntil: number = 0;\n\n  if (typeof claims === 'object') {\n    if (claims.hasOwnProperty('nbf')) {\n      validSince = claims['nbf'] as number;\n    } else if (claims.hasOwnProperty('iat')) {\n      validSince = claims['iat'] as number;\n    }\n\n    if (claims.hasOwnProperty('exp')) {\n      validUntil = claims['exp'] as number;\n    } else {\n      // token will expire after 24h by default\n      validUntil = validSince + 86400;\n    }\n  }\n\n  return (\n    !!now &&\n    !!validSince &&\n    !!validUntil &&\n    now >= validSince &&\n    now <= validUntil\n  );\n};\n\n/**\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\n *\n * Notes:\n * - May return null if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const issuedAtTime = function (token: string): number | null {\n  const claims: Claims = decode(token).claims;\n  if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n    return claims['iat'] as number;\n  }\n  return null;\n};\n\n/**\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isValidFormat = function (token: string): boolean {\n  const decoded = decode(token),\n    claims = decoded.claims;\n\n  return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n\n/**\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nexport const isAdmin = function (token: string): boolean {\n  const claims: Claims = decode(token).claims;\n  return typeof claims === 'object' && claims['admin'] === true;\n};\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function contains<T extends object>(obj: T, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function safeGet<T extends object, K extends keyof T>(\n  obj: T,\n  key: K\n): T[K] | undefined {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\n\nexport function isEmpty(obj: object): obj is {} {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function map<K extends string, V, U>(\n  obj: { [key in K]: V },\n  fn: (value: V, key: K, obj: { [key in K]: V }) => U,\n  contextObj?: unknown\n): { [key in K]: U } {\n  const res: Partial<{ [key in K]: U }> = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res as { [key in K]: U };\n}\n\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nexport function deepEqual(a: object, b: object): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n\n    const aProp = (a as Record<string, unknown>)[k];\n    const bProp = (b as Record<string, unknown>)[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isObject(thing: unknown): thing is object {\n  return thing !== null && typeof thing === 'object';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from './deferred';\n\n/**\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\n * @internal\n */\nexport function promiseWithTimeout<T>(\n  promise: Promise<T>,\n  timeInMS = 2000\n): Promise<T> {\n  const deferredPromise = new Deferred<T>();\n  setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n  promise.then(deferredPromise.resolve, deferredPromise.reject);\n  return deferredPromise.promise;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\n * params object (e.g. {arg: 'val', arg2: 'val2'})\n * Note: You must prepend it with ? when adding it to a URL.\n */\nexport function querystring(querystringParams: {\n  [key: string]: string | number;\n}): string {\n  const params = [];\n  for (const [key, value] of Object.entries(querystringParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(arrayVal => {\n        params.push(\n          encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal)\n        );\n      });\n    } else {\n      params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n    }\n  }\n  return params.length ? '&' + params.join('&') : '';\n}\n\n/**\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\n * (e.g. {arg: 'val', arg2: 'val2'})\n */\nexport function querystringDecode(querystring: string): Record<string, string> {\n  const obj: Record<string, string> = {};\n  const tokens = querystring.replace(/^\\?/, '').split('&');\n\n  tokens.forEach(token => {\n    if (token) {\n      const [key, value] = token.split('=');\n      obj[decodeURIComponent(key)] = decodeURIComponent(value);\n    }\n  });\n  return obj;\n}\n\n/**\n * Extract the query string part of a URL, including the leading question mark (if present).\n */\nexport function extractQuerystring(url: string): string {\n  const queryStart = url.indexOf('?');\n  if (!queryStart) {\n    return '';\n  }\n  const fragmentStart = url.indexOf('#', queryStart);\n  return url.substring(\n    queryStart,\n    fragmentStart > 0 ? fragmentStart : undefined\n  );\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview SHA-1 cryptographic hash.\n * Variable names follow the notation in FIPS PUB 180-3:\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\n *\n * Usage:\n *   var sha1 = new sha1();\n *   sha1.update(bytes);\n *   var hash = sha1.digest();\n *\n * Performance:\n *   Chrome 23:   ~400 Mbit/s\n *   Firefox 16:  ~250 Mbit/s\n *\n */\n\n/**\n * SHA-1 cryptographic hash constructor.\n *\n * The properties declared here are discussed in the above algorithm document.\n * @constructor\n * @final\n * @struct\n */\nexport class Sha1 {\n  /**\n   * Holds the previous values of accumulated variables a-e in the compress_\n   * function.\n   * @private\n   */\n  private chain_: number[] = [];\n\n  /**\n   * A buffer holding the partially computed hash result.\n   * @private\n   */\n  private buf_: number[] = [];\n\n  /**\n   * An array of 80 bytes, each a part of the message to be hashed.  Referred to\n   * as the message schedule in the docs.\n   * @private\n   */\n  private W_: number[] = [];\n\n  /**\n   * Contains data needed to pad messages less than 64 bytes.\n   * @private\n   */\n  private pad_: number[] = [];\n\n  /**\n   * @private {number}\n   */\n  private inbuf_: number = 0;\n\n  /**\n   * @private {number}\n   */\n  private total_: number = 0;\n\n  blockSize: number;\n\n  constructor() {\n    this.blockSize = 512 / 8;\n\n    this.pad_[0] = 128;\n    for (let i = 1; i < this.blockSize; ++i) {\n      this.pad_[i] = 0;\n    }\n\n    this.reset();\n  }\n\n  reset(): void {\n    this.chain_[0] = 0x67452301;\n    this.chain_[1] = 0xefcdab89;\n    this.chain_[2] = 0x98badcfe;\n    this.chain_[3] = 0x10325476;\n    this.chain_[4] = 0xc3d2e1f0;\n\n    this.inbuf_ = 0;\n    this.total_ = 0;\n  }\n\n  /**\n   * Internal compress helper function.\n   * @param buf Block to compress.\n   * @param offset Offset of the block in the buffer.\n   * @private\n   */\n  compress_(buf: number[] | Uint8Array | string, offset?: number): void {\n    if (!offset) {\n      offset = 0;\n    }\n\n    const W = this.W_;\n\n    // get 16 big endian words\n    if (typeof buf === 'string') {\n      for (let i = 0; i < 16; i++) {\n        // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n        // have a bug that turns the post-increment ++ operator into pre-increment\n        // during JIT compilation.  We have code that depends heavily on SHA-1 for\n        // correctness and which is affected by this bug, so I've removed all uses\n        // of post-increment ++ in which the result value is used.  We can revert\n        // this change once the Safari bug\n        // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n        // most clients have been updated.\n        W[i] =\n          (buf.charCodeAt(offset) << 24) |\n          (buf.charCodeAt(offset + 1) << 16) |\n          (buf.charCodeAt(offset + 2) << 8) |\n          buf.charCodeAt(offset + 3);\n        offset += 4;\n      }\n    } else {\n      for (let i = 0; i < 16; i++) {\n        W[i] =\n          (buf[offset] << 24) |\n          (buf[offset + 1] << 16) |\n          (buf[offset + 2] << 8) |\n          buf[offset + 3];\n        offset += 4;\n      }\n    }\n\n    // expand to 80 words\n    for (let i = 16; i < 80; i++) {\n      const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n      W[i] = ((t << 1) | (t >>> 31)) & 0xffffffff;\n    }\n\n    let a = this.chain_[0];\n    let b = this.chain_[1];\n    let c = this.chain_[2];\n    let d = this.chain_[3];\n    let e = this.chain_[4];\n    let f, k;\n\n    // TODO(user): Try to unroll this loop to speed up the computation.\n    for (let i = 0; i < 80; i++) {\n      if (i < 40) {\n        if (i < 20) {\n          f = d ^ (b & (c ^ d));\n          k = 0x5a827999;\n        } else {\n          f = b ^ c ^ d;\n          k = 0x6ed9eba1;\n        }\n      } else {\n        if (i < 60) {\n          f = (b & c) | (d & (b | c));\n          k = 0x8f1bbcdc;\n        } else {\n          f = b ^ c ^ d;\n          k = 0xca62c1d6;\n        }\n      }\n\n      const t = (((a << 5) | (a >>> 27)) + f + e + k + W[i]) & 0xffffffff;\n      e = d;\n      d = c;\n      c = ((b << 30) | (b >>> 2)) & 0xffffffff;\n      b = a;\n      a = t;\n    }\n\n    this.chain_[0] = (this.chain_[0] + a) & 0xffffffff;\n    this.chain_[1] = (this.chain_[1] + b) & 0xffffffff;\n    this.chain_[2] = (this.chain_[2] + c) & 0xffffffff;\n    this.chain_[3] = (this.chain_[3] + d) & 0xffffffff;\n    this.chain_[4] = (this.chain_[4] + e) & 0xffffffff;\n  }\n\n  update(bytes?: number[] | Uint8Array | string, length?: number): void {\n    // TODO(johnlenz): tighten the function signature and remove this check\n    if (bytes == null) {\n      return;\n    }\n\n    if (length === undefined) {\n      length = bytes.length;\n    }\n\n    const lengthMinusBlock = length - this.blockSize;\n    let n = 0;\n    // Using local instead of member variables gives ~5% speedup on Firefox 16.\n    const buf = this.buf_;\n    let inbuf = this.inbuf_;\n\n    // The outer while loop should execute at most twice.\n    while (n < length) {\n      // When we have no data in the block to top up, we can directly process the\n      // input buffer (assuming it contains sufficient data). This gives ~25%\n      // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n      // the data is provided in large chunks (or in multiples of 64 bytes).\n      if (inbuf === 0) {\n        while (n <= lengthMinusBlock) {\n          this.compress_(bytes, n);\n          n += this.blockSize;\n        }\n      }\n\n      if (typeof bytes === 'string') {\n        while (n < length) {\n          buf[inbuf] = bytes.charCodeAt(n);\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      } else {\n        while (n < length) {\n          buf[inbuf] = bytes[n];\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      }\n    }\n\n    this.inbuf_ = inbuf;\n    this.total_ += length;\n  }\n\n  /** @override */\n  digest(): number[] {\n    const digest: number[] = [];\n    let totalBits = this.total_ * 8;\n\n    // Add pad 0x80 0x00*.\n    if (this.inbuf_ < 56) {\n      this.update(this.pad_, 56 - this.inbuf_);\n    } else {\n      this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n    }\n\n    // Add # bits.\n    for (let i = this.blockSize - 1; i >= 56; i--) {\n      this.buf_[i] = totalBits & 255;\n      totalBits /= 256; // Don't use bit-shifting here!\n    }\n\n    this.compress_(this.buf_);\n\n    let n = 0;\n    for (let i = 0; i < 5; i++) {\n      for (let j = 24; j >= 0; j -= 8) {\n        digest[n] = (this.chain_[i] >> j) & 255;\n        ++n;\n      }\n    }\n    return digest;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport type NextFn<T> = (value: T) => void;\nexport type ErrorFn = (error: Error) => void;\nexport type CompleteFn = () => void;\n\nexport interface Observer<T> {\n  // Called once for each value in a stream of values.\n  next: NextFn<T>;\n\n  // A stream terminates by a single call to EITHER error() or complete().\n  error: ErrorFn;\n\n  // No events will be sent to next() once complete() is called.\n  complete: CompleteFn;\n}\n\nexport type PartialObserver<T> = Partial<Observer<T>>;\n\n// TODO: Support also Unsubscribe.unsubscribe?\nexport type Unsubscribe = () => void;\n\n/**\n * The Subscribe interface has two forms - passing the inline function\n * callbacks, or a object interface with callback properties.\n */\nexport interface Subscribe<T> {\n  (next?: NextFn<T>, error?: ErrorFn, complete?: CompleteFn): Unsubscribe;\n  (observer: PartialObserver<T>): Unsubscribe;\n}\n\nexport interface Observable<T> {\n  // Subscribe method\n  subscribe: Subscribe<T>;\n}\n\nexport type Executor<T> = (observer: Observer<T>) => void;\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nexport function createSubscribe<T>(\n  executor: Executor<T>,\n  onNoObservers?: Executor<T>\n): Subscribe<T> {\n  const proxy = new ObserverProxy<T>(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy<T> implements Observer<T> {\n  private observers: Array<Observer<T>> | undefined = [];\n  private unsubscribes: Unsubscribe[] = [];\n  private onNoObservers: Executor<T> | undefined;\n  private observerCount = 0;\n  // Micro-task scheduling by calling task.then().\n  private task = Promise.resolve();\n  private finalized = false;\n  private finalError?: Error;\n\n  /**\n   * @param executor Function which can make calls to a single Observer\n   *     as a proxy.\n   * @param onNoObservers Callback when count of Observers goes to zero.\n   */\n  constructor(executor: Executor<T>, onNoObservers?: Executor<T>) {\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task\n      .then(() => {\n        executor(this);\n      })\n      .catch(e => {\n        this.error(e);\n      });\n  }\n\n  next(value: T): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.next(value);\n    });\n  }\n\n  error(error: Error): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n\n  complete(): void {\n    this.forEachObserver((observer: Observer<T>) => {\n      observer.complete();\n    });\n    this.close();\n  }\n\n  /**\n   * Subscribe function that can be used to add an Observer to the fan-out list.\n   *\n   * - We require that no event is sent to a subscriber synchronously to their\n   *   call to subscribe().\n   */\n  subscribe(\n    nextOrObserver?: NextFn<T> | PartialObserver<T>,\n    error?: ErrorFn,\n    complete?: CompleteFn\n  ): Unsubscribe {\n    let observer: Observer<T>;\n\n    if (\n      nextOrObserver === undefined &&\n      error === undefined &&\n      complete === undefined\n    ) {\n      throw new Error('Missing Observer.');\n    }\n\n    // Assemble an Observer object when passed as callback functions.\n    if (\n      implementsAnyMethods(nextOrObserver as { [key: string]: unknown }, [\n        'next',\n        'error',\n        'complete'\n      ])\n    ) {\n      observer = nextOrObserver as Observer<T>;\n    } else {\n      observer = {\n        next: nextOrObserver as NextFn<T>,\n        error,\n        complete\n      } as Observer<T>;\n    }\n\n    if (observer.next === undefined) {\n      observer.next = noop as NextFn<T>;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop as ErrorFn;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop as CompleteFn;\n    }\n\n    const unsub = this.unsubscribeOne.bind(this, this.observers!.length);\n\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n\n    this.observers!.push(observer as Observer<T>);\n\n    return unsub;\n  }\n\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  private unsubscribeOne(i: number): void {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n\n    delete this.observers[i];\n\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n\n  private forEachObserver(fn: (observer: Observer<T>) => void): void {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers!.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  private sendOne(i: number, fn: (observer: Observer<T>) => void): void {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n\n  private close(err?: Error): void {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function async(fn: Function, onError?: ErrorFn): Function {\n  return (...args: unknown[]) => {\n    Promise.resolve(true)\n      .then(() => {\n        fn(...args);\n      })\n      .catch((error: Error) => {\n        if (onError) {\n          onError(error);\n        }\n      });\n  };\n}\n\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(\n  obj: { [key: string]: unknown },\n  methods: string[]\n): boolean {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction noop(): void {\n  // do nothing\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Check to make sure the appropriate number of arguments are provided for a public function.\n * Throws an error if it fails.\n *\n * @param fnName The function name\n * @param minCount The minimum number of arguments to allow for the function call\n * @param maxCount The maximum number of argument to allow for the function call\n * @param argCount The actual number of arguments provided.\n */\nexport const validateArgCount = function (\n  fnName: string,\n  minCount: number,\n  maxCount: number,\n  argCount: number\n): void {\n  let argError;\n  if (argCount < minCount) {\n    argError = 'at least ' + minCount;\n  } else if (argCount > maxCount) {\n    argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n  }\n  if (argError) {\n    const error =\n      fnName +\n      ' failed: Was called with ' +\n      argCount +\n      (argCount === 1 ? ' argument.' : ' arguments.') +\n      ' Expects ' +\n      argError +\n      '.';\n    throw new Error(error);\n  }\n};\n\n/**\n * Generates a string to prefix an error message about failed argument validation\n *\n * @param fnName The function name\n * @param argName The name of the argument\n * @return The prefix to add to the error thrown for validation.\n */\nexport function errorPrefix(fnName: string, argName: string): string {\n  return `${fnName} failed: ${argName} argument `;\n}\n\n/**\n * @param fnName\n * @param argumentNumber\n * @param namespace\n * @param optional\n */\nexport function validateNamespace(\n  fnName: string,\n  namespace: string,\n  optional: boolean\n): void {\n  if (optional && !namespace) {\n    return;\n  }\n  if (typeof namespace !== 'string') {\n    //TODO: I should do more validation here. We only allow certain chars in namespaces.\n    throw new Error(\n      errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.'\n    );\n  }\n}\n\nexport function validateCallback(\n  fnName: string,\n  argumentName: string,\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  callback: Function,\n  optional: boolean\n): void {\n  if (optional && !callback) {\n    return;\n  }\n  if (typeof callback !== 'function') {\n    throw new Error(\n      errorPrefix(fnName, argumentName) + 'must be a valid function.'\n    );\n  }\n}\n\nexport function validateContextObject(\n  fnName: string,\n  argumentName: string,\n  context: unknown,\n  optional: boolean\n): void {\n  if (optional && !context) {\n    return;\n  }\n  if (typeof context !== 'object' || context === null) {\n    throw new Error(\n      errorPrefix(fnName, argumentName) + 'must be a valid context object.'\n    );\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { assert } from './assert';\n\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in JavaScript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n\n/**\n * @param {string} str\n * @return {Array}\n */\nexport const stringToByteArray = function (str: string): number[] {\n  const out: number[] = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n\n    // Is this the lead surrogate in a surrogate pair?\n    if (c >= 0xd800 && c <= 0xdbff) {\n      const high = c - 0xd800; // the high 10 bits.\n      i++;\n      assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n      const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n      c = 0x10000 + (high << 10) + low;\n    }\n\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = (c >> 6) | 192;\n      out[p++] = (c & 63) | 128;\n    } else if (c < 65536) {\n      out[p++] = (c >> 12) | 224;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    } else {\n      out[p++] = (c >> 18) | 240;\n      out[p++] = ((c >> 12) & 63) | 128;\n      out[p++] = ((c >> 6) & 63) | 128;\n      out[p++] = (c & 63) | 128;\n    }\n  }\n  return out;\n};\n\n/**\n * Calculate length without actually converting; useful for doing cheaper validation.\n * @param {string} str\n * @return {number}\n */\nexport const stringLength = function (str: string): number {\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c < 128) {\n      p++;\n    } else if (c < 2048) {\n      p += 2;\n    } else if (c >= 0xd800 && c <= 0xdbff) {\n      // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n      p += 4;\n      i++; // skip trail surrogate.\n    } else {\n      p += 3;\n    }\n  }\n  return p;\n};\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nexport const MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nexport const RANDOM_FACTOR = 0.5;\n\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nexport function calculateBackoffMillis(\n  backoffCount: number,\n  intervalMillis: number = DEFAULT_INTERVAL_MILLIS,\n  backoffFactor: number = DEFAULT_BACKOFF_FACTOR\n): number {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n    // A fraction of the backoff value to add/subtract.\n    // Deviation: changes multiplication order to improve readability.\n    RANDOM_FACTOR *\n      currBaseValue *\n      // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n      // if we add or subtract.\n      (Math.random() - 0.5) *\n      2\n  );\n\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Provide English ordinal letters after a number\n */\nexport function ordinal(i: number): string {\n  if (!Number.isFinite(i)) {\n    return `${i}`;\n  }\n  return i + indicator(i);\n}\n\nfunction indicator(i: number): string {\n  i = Math.abs(i);\n  const cent = i % 100;\n  if (cent >= 10 && cent <= 20) {\n    return 'th';\n  }\n  const dec = i % 10;\n  if (dec === 1) {\n    return 'st';\n  }\n  if (dec === 2) {\n    return 'nd';\n  }\n  if (dec === 3) {\n    return 'rd';\n  }\n  return 'th';\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface Compat<T> {\n  _delegate: T;\n}\n\nexport function getModularInstance<ExpService>(\n  service: Compat<ExpService> | ExpService\n): ExpService {\n  if (service && (service as Compat<ExpService>)._delegate) {\n    return (service as Compat<ExpService>)._delegate;\n  } else {\n    return service as ExpService;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private readonly instancesOptions: Map<string, Record<string, unknown>> =\n    new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier: string = DEFAULT_ENTRY_NAME): Record<string, unknown> {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance!);\n      this.instancesOptions.set(instanceIdentifier, options);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance!, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance!\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return this.providers.get(name) as unknown as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, provider as unknown as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ComponentContainer,\n  ComponentType,\n  Provider,\n  Name\n} from '@firebase/component';\nimport { PlatformLoggerService, VersionService } from './types';\n\nexport class PlatformLoggerServiceImpl implements PlatformLoggerService {\n  constructor(private readonly container: ComponentContainer) {}\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString(): string {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers\n      .map(provider => {\n        if (isVersionServiceProvider(provider)) {\n          const service = provider.getImmediate() as VersionService;\n          return `${service.library}/${service.version}`;\n        } else {\n          return null;\n        }\n      })\n      .filter(logString => logString)\n      .join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider: Provider<Name>): boolean {\n  const component = provider.getComponent();\n  return component?.type === ComponentType.VERSION;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name as appName } from '../package.json';\nimport { name as appCompatName } from '../../app-compat/package.json';\nimport { name as analyticsCompatName } from '../../../packages/analytics-compat/package.json';\nimport { name as analyticsName } from '../../../packages/analytics/package.json';\nimport { name as appCheckCompatName } from '../../../packages/app-check-compat/package.json';\nimport { name as appCheckName } from '../../../packages/app-check/package.json';\nimport { name as authName } from '../../../packages/auth/package.json';\nimport { name as authCompatName } from '../../../packages/auth-compat/package.json';\nimport { name as databaseName } from '../../../packages/database/package.json';\nimport { name as dataconnectName } from '../../../packages/data-connect/package.json';\nimport { name as databaseCompatName } from '../../../packages/database-compat/package.json';\nimport { name as functionsName } from '../../../packages/functions/package.json';\nimport { name as functionsCompatName } from '../../../packages/functions-compat/package.json';\nimport { name as installationsName } from '../../../packages/installations/package.json';\nimport { name as installationsCompatName } from '../../../packages/installations-compat/package.json';\nimport { name as messagingName } from '../../../packages/messaging/package.json';\nimport { name as messagingCompatName } from '../../../packages/messaging-compat/package.json';\nimport { name as performanceName } from '../../../packages/performance/package.json';\nimport { name as performanceCompatName } from '../../../packages/performance-compat/package.json';\nimport { name as remoteConfigName } from '../../../packages/remote-config/package.json';\nimport { name as remoteConfigCompatName } from '../../../packages/remote-config-compat/package.json';\nimport { name as storageName } from '../../../packages/storage/package.json';\nimport { name as storageCompatName } from '../../../packages/storage-compat/package.json';\nimport { name as firestoreName } from '../../../packages/firestore/package.json';\nimport { name as aiName } from '../../../packages/ai/package.json';\nimport { name as firestoreCompatName } from '../../../packages/firestore-compat/package.json';\nimport { name as packageName } from '../../../packages/firebase/package.json';\n\n/**\n * The default app name\n *\n * @internal\n */\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\nexport const PLATFORM_LOG_STRING = {\n  [appName]: 'fire-core',\n  [appCompatName]: 'fire-core-compat',\n  [analyticsName]: 'fire-analytics',\n  [analyticsCompatName]: 'fire-analytics-compat',\n  [appCheckName]: 'fire-app-check',\n  [appCheckCompatName]: 'fire-app-check-compat',\n  [authName]: 'fire-auth',\n  [authCompatName]: 'fire-auth-compat',\n  [databaseName]: 'fire-rtdb',\n  [dataconnectName]: 'fire-data-connect',\n  [databaseCompatName]: 'fire-rtdb-compat',\n  [functionsName]: 'fire-fn',\n  [functionsCompatName]: 'fire-fn-compat',\n  [installationsName]: 'fire-iid',\n  [installationsCompatName]: 'fire-iid-compat',\n  [messagingName]: 'fire-fcm',\n  [messagingCompatName]: 'fire-fcm-compat',\n  [performanceName]: 'fire-perf',\n  [performanceCompatName]: 'fire-perf-compat',\n  [remoteConfigName]: 'fire-rc',\n  [remoteConfigCompatName]: 'fire-rc-compat',\n  [storageName]: 'fire-gcs',\n  [storageCompatName]: 'fire-gcs-compat',\n  [firestoreName]: 'fire-fst',\n  [firestoreCompatName]: 'fire-fst-compat',\n  [aiName]: 'fire-vertex',\n  'fire-js': 'fire-js', // Platform identifier for JS SDK.\n  [packageName]: 'fire-js-all'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings,\n  FirebaseOptions,\n  FirebaseServerApp\n} from './public-types';\nimport { Component, Provider, Name } from '@firebase/component';\nimport { logger } from './logger';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\n\n/**\n * @internal\n */\nexport const _apps = new Map<string, FirebaseApp>();\n\n/**\n * @internal\n */\nexport const _serverApps = new Map<string, FirebaseServerApp>();\n\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const _components = new Map<string, Component<any>>();\n\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nexport function _addComponent<T extends Name>(\n  app: FirebaseApp,\n  component: Component<T>\n): void {\n  try {\n    (app as FirebaseAppImpl).container.addComponent(component);\n  } catch (e) {\n    logger.debug(\n      `Component ${component.name} failed to register with FirebaseApp ${app.name}`,\n      e\n    );\n  }\n}\n\n/**\n *\n * @internal\n */\nexport function _addOrOverwriteComponent(\n  app: FirebaseApp,\n  component: Component\n): void {\n  (app as FirebaseAppImpl).container.addOrOverwriteComponent(component);\n}\n\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nexport function _registerComponent<T extends Name>(\n  component: Component<T>\n): boolean {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(\n      `There were multiple attempts to register component ${componentName}.`\n    );\n\n    return false;\n  }\n\n  _components.set(componentName, component);\n\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app as FirebaseAppImpl, component);\n  }\n\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp as FirebaseServerAppImpl, component);\n  }\n\n  return true;\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nexport function _getProvider<T extends Name>(\n  app: FirebaseApp,\n  name: T\n): Provider<T> {\n  const heartbeatController = (app as FirebaseAppImpl).container\n    .getProvider('heartbeat')\n    .getImmediate({ optional: true });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return (app as FirebaseAppImpl).container.getProvider(name);\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nexport function _removeServiceInstance<T extends Name>(\n  app: FirebaseApp,\n  name: T,\n  instanceIdentifier: string = DEFAULT_ENTRY_NAME\n): void {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp, FirebaseOptions or FirebaseAppSettings.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nexport function _isFirebaseApp(\n  obj: FirebaseApp | FirebaseOptions | FirebaseAppSettings\n): obj is FirebaseApp {\n  return (obj as FirebaseApp).options !== undefined;\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp, FirebaseOptions or FirebaseAppSettings.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerAppSettings(\n  obj: FirebaseApp | FirebaseOptions | FirebaseAppSettings\n): obj is FirebaseServerAppSettings {\n  if (_isFirebaseApp(obj)) {\n    return false;\n  }\n  return (\n    'authIdToken' in obj ||\n    'appCheckToken' in obj ||\n    'releaseOnDeref' in obj ||\n    'automaticDataCollectionEnabled' in obj\n  );\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerApp(\n  obj: FirebaseApp | FirebaseServerApp | null | undefined\n): obj is FirebaseServerApp {\n  if (obj === null || obj === undefined) {\n    return false;\n  }\n  return (obj as FirebaseServerApp).settings !== undefined;\n}\n\n/**\n * Test only\n *\n * @internal\n */\nexport function _clearComponents(): void {\n  _components.clear();\n}\n\n/**\n * Exported in order to be used in app-compat package\n */\nexport { DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  BAD_APP_NAME = 'bad-app-name',\n  DUPLICATE_APP = 'duplicate-app',\n  APP_DELETED = 'app-deleted',\n  SERVER_APP_DELETED = 'server-app-deleted',\n  NO_OPTIONS = 'no-options',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument',\n  INVALID_LOG_ARGUMENT = 'invalid-log-argument',\n  IDB_OPEN = 'idb-open',\n  IDB_GET = 'idb-get',\n  IDB_WRITE = 'idb-set',\n  IDB_DELETE = 'idb-delete',\n  FINALIZATION_REGISTRY_NOT_SUPPORTED = 'finalization-registry-not-supported',\n  INVALID_SERVER_APP_ENVIRONMENT = 'invalid-server-app-environment'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call initializeApp() first',\n  [AppError.BAD_APP_NAME]: \"Illegal App name: '{$appName}'\",\n  [AppError.DUPLICATE_APP]:\n    \"Firebase App named '{$appName}' already exists with different options or config\",\n  [AppError.APP_DELETED]: \"Firebase App named '{$appName}' already deleted\",\n  [AppError.SERVER_APP_DELETED]: 'Firebase Server App has been deleted',\n  [AppError.NO_OPTIONS]:\n    'Need to provide options, when not being deployed to hosting via source.',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.',\n  [AppError.INVALID_LOG_ARGUMENT]:\n    'First argument to `onLog` must be null or a function.',\n  [AppError.IDB_OPEN]:\n    'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_GET]:\n    'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_WRITE]:\n    'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_DELETE]:\n    'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]:\n    'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [AppError.INVALID_SERVER_APP_ENVIRONMENT]:\n    'FirebaseServerApp is not for use in browser environments.'\n};\n\ninterface ErrorParams {\n  [AppError.NO_APP]: { appName: string };\n  [AppError.BAD_APP_NAME]: { appName: string };\n  [AppError.DUPLICATE_APP]: { appName: string };\n  [AppError.APP_DELETED]: { appName: string };\n  [AppError.INVALID_APP_ARGUMENT]: { appName: string };\n  [AppError.IDB_OPEN]: { originalErrorMessage?: string };\n  [AppError.IDB_GET]: { originalErrorMessage?: string };\n  [AppError.IDB_WRITE]: { originalErrorMessage?: string };\n  [AppError.IDB_DELETE]: { originalErrorMessage?: string };\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]: { appName?: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseAppSettings\n} from './public-types';\nimport {\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { ERROR_FACTORY, AppError } from './errors';\n\nexport class FirebaseAppImpl implements FirebaseApp {\n  protected readonly _options: FirebaseOptions;\n  protected readonly _name: string;\n  /**\n   * Original config values passed in as a constructor parameter.\n   * It is only used to compare with another config object to support idempotent initializeApp().\n   *\n   * Updating automaticDataCollectionEnabled on the App instance will not change its value in _config.\n   */\n  private readonly _config: Required<FirebaseAppSettings>;\n  private _automaticDataCollectionEnabled: boolean;\n  protected _isDeleted = false;\n  private readonly _container: ComponentContainer;\n\n  constructor(\n    options: FirebaseOptions,\n    config: Required<FirebaseAppSettings>,\n    container: ComponentContainer\n  ) {\n    this._options = { ...options };\n    this._config = { ...config };\n    this._name = config.name;\n    this._automaticDataCollectionEnabled =\n      config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(\n      new Component('app', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val: boolean) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    this.checkDestroyed();\n    return this._name;\n  }\n\n  get options(): FirebaseOptions {\n    this.checkDestroyed();\n    return this._options;\n  }\n\n  get config(): Required<FirebaseAppSettings> {\n    this.checkDestroyed();\n    return this._config;\n  }\n\n  get container(): ComponentContainer {\n    return this._container;\n  }\n\n  get isDeleted(): boolean {\n    return this._isDeleted;\n  }\n\n  set isDeleted(val: boolean) {\n    this._isDeleted = val;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.APP_DELETED, { appName: this._name });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseAppSettings,\n  FirebaseServerApp,\n  FirebaseServerAppSettings,\n  FirebaseOptions\n} from './public-types';\nimport { deleteApp, registerVersion } from './api';\nimport { ComponentContainer } from '@firebase/component';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { name as packageName, version } from '../package.json';\nimport { base64Decode } from '@firebase/util';\n\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token: string, tokenName: string): void {\n  const secondPart = base64Decode(base64Token.split('.')[1]);\n  if (secondPart === null) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`\n    );\n    return;\n  }\n  const expClaim = JSON.parse(secondPart).exp;\n  if (expClaim === undefined) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`\n    );\n    return;\n  }\n  const exp = JSON.parse(secondPart).exp * 1000;\n  const now = new Date().getTime();\n  const diff = exp - now;\n  if (diff <= 0) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: the token has expired.`\n    );\n  }\n}\n\nexport class FirebaseServerAppImpl\n  extends FirebaseAppImpl\n  implements FirebaseServerApp\n{\n  private readonly _serverConfig: FirebaseServerAppSettings;\n  private _finalizationRegistry: FinalizationRegistry<object> | null;\n  private _refCount: number;\n\n  constructor(\n    options: FirebaseOptions | FirebaseAppImpl,\n    serverConfig: FirebaseServerAppSettings,\n    name: string,\n    container: ComponentContainer\n  ) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled =\n      serverConfig.automaticDataCollectionEnabled !== undefined\n        ? serverConfig.automaticDataCollectionEnabled\n        : true;\n\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config: Required<FirebaseAppSettings> = {\n      name,\n      automaticDataCollectionEnabled\n    };\n\n    if ((options as FirebaseOptions).apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options as FirebaseOptions, config, container);\n    } else {\n      const appImpl: FirebaseAppImpl = options as FirebaseAppImpl;\n      super(appImpl.options, config, container);\n    }\n\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = {\n      automaticDataCollectionEnabled,\n      ...serverConfig\n    };\n\n    // Ensure that the current time is within the `authIdtoken` window of validity.\n    if (this._serverConfig.authIdToken) {\n      validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n    }\n\n    // Ensure that the current time is within the `appCheckToken` window of validity.\n    if (this._serverConfig.appCheckToken) {\n      validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n    }\n\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n\n    registerVersion(packageName, version, 'serverapp');\n  }\n\n  toJSON(): undefined {\n    return undefined;\n  }\n\n  get refCount(): number {\n    return this._refCount;\n  }\n\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj: object | undefined): void {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n\n  // Decrement the reference count.\n  decRefCount(): number {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  private automaticCleanup(): void {\n    void deleteApp(this);\n  }\n\n  get settings(): FirebaseServerAppSettings {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.SERVER_APP_DELETED);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseServerApp,\n  FirebaseOptions,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings\n} from './public-types';\nimport { DEFAULT_ENTRY_NAME, PLATFORM_LOG_STRING } from './constants';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport {\n  ComponentContainer,\n  Component,\n  Name,\n  ComponentType\n} from '@firebase/component';\nimport { version } from '../../firebase/package.json';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\nimport {\n  _apps,\n  _components,\n  _isFirebaseApp,\n  _isFirebaseServerAppSettings,\n  _registerComponent,\n  _serverApps\n} from './internal';\nimport { logger } from './logger';\nimport {\n  LogLevelString,\n  setLogLevel as setLogLevelImpl,\n  LogCallback,\n  LogOptions,\n  setUserLogHandler\n} from '@firebase/logger';\nimport {\n  deepEqual,\n  getDefaultAppConfig,\n  isBrowser,\n  isWebWorker\n} from '@firebase/util';\n\nexport { FirebaseError } from '@firebase/util';\n\n/**\n * The current SDK version.\n *\n * @public\n */\nexport const SDK_VERSION = version;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseApp} instance.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize default app\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeApp({\n *   apiKey: \"AIza....\",                             // Auth / General Use\n *   authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *   databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *   storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *   messagingSenderId: \"123456789\"                  // Cloud Messaging\n * });\n * ```\n *\n * @example\n * ```javascript\n *\n * // Initialize another app\n * const otherApp = initializeApp({\n *   databaseURL: \"https://<OTHER_DATABASE_NAME>.firebaseio.com\",\n *   storageBucket: \"<OTHER_STORAGE_BUCKET>.appspot.com\"\n * }, \"otherApp\");\n * ```\n *\n * @param options - Options to configure the app's services.\n * @param name - Optional name of the app to initialize. If no name\n *   is provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The initialized app.\n *\n * @throws If the optional `name` parameter is malformed or empty.\n *\n * @throws If a `FirebaseApp` already exists with the same name but with a different configuration.\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  name?: string\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @param options - Options to configure the app's services.\n * @param config - FirebaseApp Configuration\n *\n * @throws If {@link FirebaseAppSettings.name} is defined but the value is malformed or empty.\n *\n * @throws If a `FirebaseApp` already exists with the same name but with a different configuration.\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  config?: FirebaseAppSettings\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @public\n */\nexport function initializeApp(): FirebaseApp;\nexport function initializeApp(\n  _options?: FirebaseOptions,\n  rawConfig = {}\n): FirebaseApp {\n  let options = _options;\n\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = { name };\n  }\n\n  const config: Required<FirebaseAppSettings> = {\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: true,\n    ...rawConfig\n  };\n  const name = config.name;\n\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(AppError.BAD_APP_NAME, {\n      appName: String(name)\n    });\n  }\n\n  options ||= getDefaultAppConfig();\n\n  if (!options) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  const existingApp = _apps.get(name) as FirebaseAppImpl;\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (\n      deepEqual(options, existingApp.options) &&\n      deepEqual(config, existingApp.config)\n    ) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(AppError.DUPLICATE_APP, { appName: name });\n    }\n  }\n\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseAppImpl(options, config, container);\n\n  _apps.set(name, newApp);\n\n  return newApp;\n}\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * The `FirebaseServerApp` is similar to `FirebaseApp`, but is intended for execution in\n * server side rendering environments only. Initialization will fail if invoked from a\n * browser environment.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize an instance of `FirebaseServerApp`.\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeServerApp({\n *     apiKey: \"AIza....\",                             // Auth / General Use\n *     authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *     databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *     storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *     messagingSenderId: \"123456789\"                  // Cloud Messaging\n *   },\n *   {\n *    authIdToken: \"Your Auth ID Token\"\n *   });\n * ```\n *\n * @param options - `Firebase.AppOptions` to configure the app's services, or a\n *   a `FirebaseApp` instance which contains the `AppOptions` within.\n * @param config - Optional `FirebaseServerApp` settings.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @throws If invoked in an unsupported non-server environment such as a browser.\n *\n * @throws If {@link FirebaseServerAppSettings.releaseOnDeref} is defined but the runtime doesn't\n *   provide Finalization Registry support.\n *\n * @public\n */\nexport function initializeServerApp(\n  options: FirebaseOptions | FirebaseApp,\n  config?: FirebaseServerAppSettings\n): FirebaseServerApp;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * @param config - Optional `FirebaseServerApp` settings.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @throws If invoked in an unsupported non-server environment such as a browser.\n * @throws If {@link FirebaseServerAppSettings.releaseOnDeref} is defined but the runtime doesn't\n *   provide Finalization Registry support.\n * @throws If the `FIREBASE_OPTIONS` environment variable does not contain a valid project\n *   configuration required for auto-initialization.\n *\n * @public\n */\nexport function initializeServerApp(\n  config?: FirebaseServerAppSettings\n): FirebaseServerApp;\nexport function initializeServerApp(\n  _options?: FirebaseApp | FirebaseServerAppSettings | FirebaseOptions,\n  _serverAppConfig: FirebaseServerAppSettings = {}\n): FirebaseServerApp {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(AppError.INVALID_SERVER_APP_ENVIRONMENT);\n  }\n\n  let firebaseOptions: FirebaseOptions | undefined;\n  let serverAppSettings: FirebaseServerAppSettings = _serverAppConfig || {};\n\n  if (_options) {\n    if (_isFirebaseApp(_options)) {\n      firebaseOptions = _options.options;\n    } else if (_isFirebaseServerAppSettings(_options)) {\n      serverAppSettings = _options;\n    } else {\n      firebaseOptions = _options;\n    }\n  }\n\n  if (serverAppSettings.automaticDataCollectionEnabled === undefined) {\n    serverAppSettings.automaticDataCollectionEnabled = true;\n  }\n\n  firebaseOptions ||= getDefaultAppConfig();\n  if (!firebaseOptions) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = {\n    ...serverAppSettings,\n    ...firebaseOptions\n  };\n\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n\n  const hashCode = (s: string): number => {\n    return [...s].reduce(\n      (hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0,\n      0\n    );\n  };\n\n  if (serverAppSettings.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\n        AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED,\n        {}\n      );\n    }\n  }\n\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString) as FirebaseServerApp;\n  if (existingApp) {\n    (existingApp as FirebaseServerAppImpl).incRefCount(\n      serverAppSettings.releaseOnDeref\n    );\n    return existingApp;\n  }\n\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseServerAppImpl(\n    firebaseOptions,\n    serverAppSettings,\n    nameString,\n    container\n  );\n\n  _serverApps.set(nameString, newApp);\n\n  return newApp;\n}\n\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nexport function getApp(name: string = DEFAULT_ENTRY_NAME): FirebaseApp {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n  }\n\n  return app;\n}\n\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nexport function getApps(): FirebaseApp[] {\n  return Array.from(_apps.values());\n}\n\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nexport async function deleteApp(app: FirebaseApp): Promise<void> {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app as FirebaseServerAppImpl;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n\n  if (cleanupProviders) {\n    await Promise.all(\n      (app as FirebaseAppImpl).container\n        .getProviders()\n        .map(provider => provider.delete())\n    );\n    (app as FirebaseAppImpl).isDeleted = true;\n  }\n}\n\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nexport function registerVersion(\n  libraryKeyOrName: string,\n  version: string,\n  variant?: string\n): void {\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = PLATFORM_LOG_STRING[libraryKeyOrName] ?? libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [\n      `Unable to register library \"${library}\" with version \"${version}\":`\n    ];\n    if (libraryMismatch) {\n      warning.push(\n        `library name \"${library}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(\n        `version name \"${version}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(\n    new Component(\n      `${library}-version` as Name,\n      () => ({ library, version }),\n      ComponentType.VERSION\n    )\n  );\n}\n\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nexport function onLog(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(AppError.INVALID_LOG_ARGUMENT);\n  }\n  setUserLogHandler(logCallback, options);\n}\n\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nexport function setLogLevel(logLevel: LogLevelString): void {\n  setLogLevelImpl(logLevel);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { DBSchema, openDB, IDBPDatabase } from 'idb';\nimport { AppError, ERROR_FACTORY } from './errors';\nimport { FirebaseApp } from './public-types';\nimport { HeartbeatsInIndexedDB } from './types';\nimport { logger } from './logger';\n\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\n\ninterface AppDB extends DBSchema {\n  'firebase-heartbeat-store': {\n    key: string;\n    value: HeartbeatsInIndexedDB;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<AppDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<AppDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB<AppDB>(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(AppError.IDB_OPEN, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\n\nexport async function readHeartbeatsFromIndexedDB(\n  app: FirebaseApp\n): Promise<HeartbeatsInIndexedDB | undefined> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_GET, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nexport async function writeHeartbeatsToIndexedDB(\n  app: FirebaseApp,\n  heartbeatObject: HeartbeatsInIndexedDB\n): Promise<void> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_WRITE, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.name}!${app.options.appId}`;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ComponentContainer } from '@firebase/component';\nimport {\n  base64urlEncodeWithoutPadding,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport {\n  readHeartbeatsFromIndexedDB,\n  writeHeartbeatsToIndexedDB\n} from './indexeddb';\nimport { FirebaseApp } from './public-types';\nimport {\n  HeartbeatsByUserAgent,\n  HeartbeatService,\n  HeartbeatsInIndexedDB,\n  HeartbeatStorage,\n  SingleDateHeartbeat\n} from './types';\nimport { logger } from './logger';\n\nconst MAX_HEADER_BYTES = 1024;\nexport const MAX_NUM_STORED_HEARTBEATS = 30;\n\nexport class HeartbeatServiceImpl implements HeartbeatService {\n  /**\n   * The persistence layer for heartbeats\n   * Leave public for easier testing.\n   */\n  _storage: HeartbeatStorageImpl;\n\n  /**\n   * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n   * the header string.\n   * Stores one record per date. This will be consolidated into the standard\n   * format of one record per user agent string before being sent as a header.\n   * Populated from indexedDB when the controller is instantiated and should\n   * be kept in sync with indexedDB.\n   * Leave public for easier testing.\n   */\n  _heartbeatsCache: HeartbeatsInIndexedDB | null = null;\n\n  /**\n   * the initialization promise for populating heartbeatCache.\n   * If getHeartbeatsHeader() is called before the promise resolves\n   * (heartbeatsCache == null), it should wait for this promise\n   * Leave public for easier testing.\n   */\n  _heartbeatsCachePromise: Promise<HeartbeatsInIndexedDB>;\n  constructor(private readonly container: ComponentContainer) {\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat(): Promise<void> {\n    try {\n      const platformLogger = this.container\n        .getProvider('platform-logger')\n        .getImmediate();\n\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (this._heartbeatsCache?.heartbeats == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (this._heartbeatsCache?.heartbeats == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (\n        this._heartbeatsCache.lastSentHeartbeatDate === date ||\n        this._heartbeatsCache.heartbeats.some(\n          singleDateHeartbeat => singleDateHeartbeat.date === date\n        )\n      ) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({ date, agent });\n\n        // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n        // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n        if (\n          this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS\n        ) {\n          const earliestHeartbeatIdx = getEarliestHeartbeatIdx(\n            this._heartbeatsCache.heartbeats\n          );\n          this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n        }\n      }\n\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader(): Promise<string> {\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (\n        this._heartbeatsCache?.heartbeats == null ||\n        this._heartbeatsCache.heartbeats.length === 0\n      ) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(\n        this._heartbeatsCache.heartbeats\n      );\n      const headerString = base64urlEncodeWithoutPadding(\n        JSON.stringify({ version: 2, heartbeats: heartbeatsToSend })\n      );\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\n\nfunction getUTCDateString(): string {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\n\nexport function extractHeartbeatsForHeader(\n  heartbeatsCache: SingleDateHeartbeat[],\n  maxSize = MAX_HEADER_BYTES\n): {\n  heartbeatsToSend: HeartbeatsByUserAgent[];\n  unsentEntries: SingleDateHeartbeat[];\n} {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend: HeartbeatsByUserAgent[] = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(\n      hb => hb.agent === singleDateHeartbeat.agent\n    );\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\n\nexport class HeartbeatStorageImpl implements HeartbeatStorage {\n  private _canUseIndexedDBPromise: Promise<boolean>;\n  constructor(public app: FirebaseApp) {\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck(): Promise<boolean> {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable()\n        .then(() => true)\n        .catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read(): Promise<HeartbeatsInIndexedDB> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return { heartbeats: [] };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject?.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return { heartbeats: [] };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [\n          ...existingHeartbeatsObject.heartbeats,\n          ...heartbeatsObject.heartbeats\n        ]\n      });\n    }\n  }\n}\n\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nexport function countBytes(heartbeatsCache: HeartbeatsByUserAgent[]): number {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })\n  ).length;\n}\n\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nexport function getEarliestHeartbeatIdx(\n  heartbeats: SingleDateHeartbeat[]\n): number {\n  if (heartbeats.length === 0) {\n    return -1;\n  }\n\n  let earliestHeartbeatIdx = 0;\n  let earliestHeartbeatDate = heartbeats[0].date;\n\n  for (let i = 1; i < heartbeats.length; i++) {\n    if (heartbeats[i].date < earliestHeartbeatDate) {\n      earliestHeartbeatDate = heartbeats[i].date;\n      earliestHeartbeatIdx = i;\n    }\n  }\n\n  return earliestHeartbeatIdx;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Component, ComponentType } from '@firebase/component';\nimport { PlatformLoggerServiceImpl } from './platformLoggerService';\nimport { name, version } from '../package.json';\nimport { _registerComponent } from './internal';\nimport { registerVersion } from './api';\nimport { HeartbeatServiceImpl } from './heartbeatService';\n\nexport function registerCoreComponents(variant?: string): void {\n  _registerComponent(\n    new Component(\n      'platform-logger',\n      container => new PlatformLoggerServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n  _registerComponent(\n    new Component(\n      'heartbeat',\n      container => new HeartbeatServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n\n  // Register `app` package.\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n", "/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerCoreComponents } from './registerCoreComponents';\n\nexport * from './api';\nexport * from './internal';\nexport * from './public-types';\n\nregisterCoreComponents('__RUNTIME_ENV__');\n"], "mappings": ";AAAA,IAAM,6BAA6B,MAAO;;;AGiB1C,IAAMA,sBAAoB,SAAU,KAAW;AAE7C,QAAM,MAAgB,CAAA;AACtB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,IAAI,IAAI,WAAW,CAAC;AACxB,QAAI,IAAI,KAAK;AACX,UAAI,GAAG,IAAI;IACZ,WAAU,IAAI,MAAM;AACnB,UAAI,GAAG,IAAK,KAAK,IAAK;AACtB,UAAI,GAAG,IAAK,IAAI,KAAM;IACvB,YACE,IAAI,WAAY,SACjB,IAAI,IAAI,IAAI,WACX,IAAI,WAAW,IAAI,CAAC,IAAI,WAAY,OACrC;AAEA,UAAI,UAAY,IAAI,SAAW,OAAO,IAAI,WAAW,EAAE,CAAC,IAAI;AAC5D,UAAI,GAAG,IAAK,KAAK,KAAM;AACvB,UAAI,GAAG,IAAM,KAAK,KAAM,KAAM;AAC9B,UAAI,GAAG,IAAM,KAAK,IAAK,KAAM;AAC7B,UAAI,GAAG,IAAK,IAAI,KAAM;IACvB,OAAM;AACL,UAAI,GAAG,IAAK,KAAK,KAAM;AACvB,UAAI,GAAG,IAAM,KAAK,IAAK,KAAM;AAC7B,UAAI,GAAG,IAAK,IAAI,KAAM;IACvB;EACF;AACD,SAAO;AACT;AAQA,IAAM,oBAAoB,SAAU,OAAe;AAEjD,QAAM,MAAgB,CAAA;AACtB,MAAI,MAAM,GACR,IAAI;AACN,SAAO,MAAM,MAAM,QAAQ;AACzB,UAAM,KAAK,MAAM,KAAK;AACtB,QAAI,KAAK,KAAK;AACZ,UAAI,GAAG,IAAI,OAAO,aAAa,EAAE;IAClC,WAAU,KAAK,OAAO,KAAK,KAAK;AAC/B,YAAM,KAAK,MAAM,KAAK;AACtB,UAAI,GAAG,IAAI,OAAO,cAAe,KAAK,OAAO,IAAM,KAAK,EAAG;IAC5D,WAAU,KAAK,OAAO,KAAK,KAAK;AAE/B,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,MACD,KAAK,MAAM,MAAQ,KAAK,OAAO,MAAQ,KAAK,OAAO,IAAM,KAAK,MACjE;AACF,UAAI,GAAG,IAAI,OAAO,aAAa,SAAU,KAAK,GAAG;AACjD,UAAI,GAAG,IAAI,OAAO,aAAa,SAAU,IAAI,KAAK;IACnD,OAAM;AACL,YAAM,KAAK,MAAM,KAAK;AACtB,YAAM,KAAK,MAAM,KAAK;AACtB,UAAI,GAAG,IAAI,OAAO,cACd,KAAK,OAAO,MAAQ,KAAK,OAAO,IAAM,KAAK,EAAG;IAEnD;EACF;AACD,SAAO,IAAI,KAAK,EAAE;AACpB;AAsBa,IAAA,SAAiB;;;;EAI5B,gBAAgB;;;;EAKhB,gBAAgB;;;;;EAMhB,uBAAuB;;;;;EAMvB,uBAAuB;;;;;EAMvB,mBACE;;;;EAKF,IAAI,eAAY;AACd,WAAO,KAAK,oBAAoB;EACjC;;;;EAKD,IAAI,uBAAoB;AACtB,WAAO,KAAK,oBAAoB;EACjC;;;;;;;;EASD,oBAAoB,OAAO,SAAS;;;;;;;;;;EAWpC,gBAAgB,OAA8B,SAAiB;AAC7D,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,MAAM,+CAA+C;IAC5D;AAED,SAAK,MAAK;AAEV,UAAM,gBAAgB,UAClB,KAAK,wBACL,KAAK;AAET,UAAM,SAAS,CAAA;AAEf,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,YAAM,QAAQ,MAAM,CAAC;AACrB,YAAM,YAAY,IAAI,IAAI,MAAM;AAChC,YAAM,QAAQ,YAAY,MAAM,IAAI,CAAC,IAAI;AACzC,YAAM,YAAY,IAAI,IAAI,MAAM;AAChC,YAAM,QAAQ,YAAY,MAAM,IAAI,CAAC,IAAI;AAEzC,YAAM,WAAW,SAAS;AAC1B,YAAM,YAAa,QAAQ,MAAS,IAAM,SAAS;AACnD,UAAI,YAAa,QAAQ,OAAS,IAAM,SAAS;AACjD,UAAI,WAAW,QAAQ;AAEvB,UAAI,CAAC,WAAW;AACd,mBAAW;AAEX,YAAI,CAAC,WAAW;AACd,qBAAW;QACZ;MACF;AAED,aAAO,KACL,cAAc,QAAQ,GACtB,cAAc,QAAQ,GACtB,cAAc,QAAQ,GACtB,cAAc,QAAQ,CAAC;IAE1B;AAED,WAAO,OAAO,KAAK,EAAE;EACtB;;;;;;;;;EAUD,aAAa,OAAe,SAAiB;AAG3C,QAAI,KAAK,sBAAsB,CAAC,SAAS;AACvC,aAAO,KAAK,KAAK;IAClB;AACD,WAAO,KAAK,gBAAgBA,oBAAkB,KAAK,GAAG,OAAO;EAC9D;;;;;;;;;EAUD,aAAa,OAAe,SAAgB;AAG1C,QAAI,KAAK,sBAAsB,CAAC,SAAS;AACvC,aAAO,KAAK,KAAK;IAClB;AACD,WAAO,kBAAkB,KAAK,wBAAwB,OAAO,OAAO,CAAC;EACtE;;;;;;;;;;;;;;;;EAiBD,wBAAwB,OAAe,SAAgB;AACrD,SAAK,MAAK;AAEV,UAAM,gBAAgB,UAClB,KAAK,wBACL,KAAK;AAET,UAAM,SAAmB,CAAA;AAEzB,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU;AAClC,YAAM,QAAQ,cAAc,MAAM,OAAO,GAAG,CAAC;AAE7C,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AAEF,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AAEF,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,QAAQ,YAAY,cAAc,MAAM,OAAO,CAAC,CAAC,IAAI;AAC3D,QAAE;AAEF,UAAI,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,MAAM;AACpE,cAAM,IAAI,wBAAuB;MAClC;AAED,YAAM,WAAY,SAAS,IAAM,SAAS;AAC1C,aAAO,KAAK,QAAQ;AAEpB,UAAI,UAAU,IAAI;AAChB,cAAM,WAAa,SAAS,IAAK,MAAS,SAAS;AACnD,eAAO,KAAK,QAAQ;AAEpB,YAAI,UAAU,IAAI;AAChB,gBAAM,WAAa,SAAS,IAAK,MAAQ;AACzC,iBAAO,KAAK,QAAQ;QACrB;MACF;IACF;AAED,WAAO;EACR;;;;;;EAOD,QAAK;AACH,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB,CAAA;AACtB,WAAK,iBAAiB,CAAA;AACtB,WAAK,wBAAwB,CAAA;AAC7B,WAAK,wBAAwB,CAAA;AAG7B,eAAS,IAAI,GAAG,IAAI,KAAK,aAAa,QAAQ,KAAK;AACjD,aAAK,eAAe,CAAC,IAAI,KAAK,aAAa,OAAO,CAAC;AACnD,aAAK,eAAe,KAAK,eAAe,CAAC,CAAC,IAAI;AAC9C,aAAK,sBAAsB,CAAC,IAAI,KAAK,qBAAqB,OAAO,CAAC;AAClE,aAAK,sBAAsB,KAAK,sBAAsB,CAAC,CAAC,IAAI;AAG5D,YAAI,KAAK,KAAK,kBAAkB,QAAQ;AACtC,eAAK,eAAe,KAAK,qBAAqB,OAAO,CAAC,CAAC,IAAI;AAC3D,eAAK,sBAAsB,KAAK,aAAa,OAAO,CAAC,CAAC,IAAI;QAC3D;MACF;IACF;EACF;;AAMG,IAAO,0BAAP,cAAuC,MAAK;EAAlD,cAAA;;AACW,SAAI,OAAG;EACjB;AAAA;AAKM,IAAM,eAAe,SAAU,KAAW;AAC/C,QAAM,YAAYA,oBAAkB,GAAG;AACvC,SAAO,OAAO,gBAAgB,WAAW,IAAI;AAC/C;AAMO,IAAM,gCAAgC,SAAU,KAAW;AAEhE,SAAO,aAAa,GAAG,EAAE,QAAQ,OAAO,EAAE;AAC5C;AAWO,IAAM,eAAe,SAAU,KAAW;AAC/C,MAAI;AACF,WAAO,OAAO,aAAa,KAAK,IAAI;EACrC,SAAQ,GAAG;AACV,YAAQ,MAAM,yBAAyB,CAAC;EACzC;AACD,SAAO;AACT;SElWgB,YAAS;AACvB,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAO;EACR;AACD,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;EACR;AACD,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;EACR;AACD,QAAM,IAAI,MAAM,iCAAiC;AACnD;ACuBA,IAAM,wBAAwB,MAC5B,UAAW,EAAC;AAUd,IAAM,6BAA6B,MAAmC;AACpE,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,aAAa;AACxE;EACD;AACD,QAAM,qBAAqB,QAAQ,IAAI;AACvC,MAAI,oBAAoB;AACtB,WAAO,KAAK,MAAM,kBAAkB;EACrC;AACH;AAEA,IAAM,wBAAwB,MAAmC;AAC/D,MAAI,OAAO,aAAa,aAAa;AACnC;EACD;AACD,MAAI;AACJ,MAAI;AACF,YAAQ,SAAS,OAAO,MAAM,+BAA+B;EAC9D,SAAQ,GAAG;AAGV;EACD;AACD,QAAM,UAAU,SAAS,aAAa,MAAM,CAAC,CAAC;AAC9C,SAAO,WAAW,KAAK,MAAM,OAAO;AACtC;AASO,IAAM,cAAc,MAAmC;AAC5D,MAAI;AACF,WACE,2BAA4B,KAC5B,sBAAuB,KACvB,2BAA4B,KAC5B,sBAAqB;EAExB,SAAQ,GAAG;AAOV,YAAQ,KAAK,+CAA+C,CAAC,EAAE;AAC/D;EACD;AACH;AAQa,IAAA,yBAAyB,CACpC,gBAAmB;;AACI,iCAAW,MAAX,mBAAe,kBAAf,mBAA+B;;AAQ3C,IAAA,oCAAoC,CAC/C,gBACgD;AAChD,QAAM,OAAO,uBAAuB,WAAW;AAC/C,MAAI,CAAC,MAAM;AACT,WAAO;EACR;AACD,QAAM,iBAAiB,KAAK,YAAY,GAAG;AAC3C,MAAI,kBAAkB,KAAK,iBAAiB,MAAM,KAAK,QAAQ;AAC7D,UAAM,IAAI,MAAM,gBAAgB,IAAI,sCAAsC;EAC3E;AAED,QAAM,OAAO,SAAS,KAAK,UAAU,iBAAiB,CAAC,GAAG,EAAE;AAC5D,MAAI,KAAK,CAAC,MAAM,KAAK;AAEnB,WAAO,CAAC,KAAK,UAAU,GAAG,iBAAiB,CAAC,GAAG,IAAI;EACpD,OAAM;AACL,WAAO,CAAC,KAAK,UAAU,GAAG,cAAc,GAAG,IAAI;EAChD;AACH;AAMa,IAAA,sBAAsB,MAAA;;AACjC,2BAAW,MAAX,mBAAe;;AAOJ,IAAA,yBAAyB,CACpCC,UAEA;;AAAA,2BAAa,MAAb,mBAAgB,IAAIA,KAAI;;IC3Jb,iBAAQ;EAInB,cAAA;AAFA,SAAA,SAAoC,MAAK;IAAA;AACzC,SAAA,UAAqC,MAAK;IAAA;AAExC,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAU;AAC7C,WAAK,UAAU;AACf,WAAK,SAAS;IAChB,CAAC;EACF;;;;;;EAOD,aACE,UAAqD;AAErD,WAAO,CAAC,OAAO,UAAU;AACvB,UAAI,OAAO;AACT,aAAK,OAAO,KAAK;MAClB,OAAM;AACL,aAAK,QAAQ,KAAK;MACnB;AACD,UAAI,OAAO,aAAa,YAAY;AAGlC,aAAK,QAAQ,MAAM,MAAK;QAAG,CAAA;AAI3B,YAAI,SAAS,WAAW,GAAG;AACzB,mBAAS,KAAK;QACf,OAAM;AACL,mBAAS,OAAO,KAAK;QACtB;MACF;IACH;EACD;AACF;ACpCK,SAAU,mBAAmB,KAAW;AAK5C,MAAI;AACF,UAAM,OACJ,IAAI,WAAW,SAAS,KAAK,IAAI,WAAW,UAAU,IAClD,IAAI,IAAI,GAAG,EAAE,WACb;AACN,WAAO,KAAK,SAAS,wBAAwB;EAC9C,QAAO;AACN,WAAO;EACR;AACH;AAOO,eAAe,WAAW,UAAgB;AAC/C,QAAM,SAAS,MAAM,MAAM,UAAU;IACnC,aAAa;EACd,CAAA;AACD,SAAO,OAAO;AAChB;ACgDgB,SAAA,oBACd,OACA,WAAkB;AAElB,MAAI,MAAM,KAAK;AACb,UAAM,IAAI,MACR,8GAA8G;EAEjH;AAED,QAAM,SAAS;IACb,KAAK;IACL,MAAM;;AAGR,QAAM,UAAU,aAAa;AAC7B,QAAM,MAAM,MAAM,OAAO;AACzB,QAAM,MAAM,MAAM,OAAO,MAAM;AAC/B,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,sDAAsD;EACvE;AAED,QAAM,UAA2B;;IAE/B,KAAK,kCAAkC,OAAO;IAC9C,KAAK;IACL;IACA,KAAK,MAAM;IACX,WAAW;IACX;IACA,SAAS;IACT,UAAU;MACR,kBAAkB;MAClB,YAAY,CAAE;IACf;;IAGD,GAAG;;AAIL,QAAM,YAAY;AAClB,SAAO;IACL,8BAA8B,KAAK,UAAU,MAAM,CAAC;IACpD,8BAA8B,KAAK,UAAU,OAAO,CAAC;IACrD;EACD,EAAC,KAAK,GAAG;AACZ;AAKA,IAAM,iBAAoC,CAAA;AAQ1C,SAAS,qBAAkB;AACzB,QAAM,UAA2B;IAC/B,MAAM,CAAE;IACR,UAAU,CAAE;;AAEd,aAAW,OAAO,OAAO,KAAK,cAAc,GAAG;AAC7C,QAAI,eAAe,GAAG,GAAG;AACvB,cAAQ,SAAS,KAAK,GAAG;IAC1B,OAAM;AACL,cAAQ,KAAK,KAAK,GAAG;IACtB;EACF;AACD,SAAO;AACT;AAEA,SAAS,cAAc,IAAU;AAC/B,MAAI,YAAY,SAAS,eAAe,EAAE;AAC1C,MAAI,UAAU;AACd,MAAI,CAAC,WAAW;AACd,gBAAY,SAAS,cAAc,KAAK;AACxC,cAAU,aAAa,MAAM,EAAE;AAC/B,cAAU;EACX;AACD,SAAO,EAAE,SAAS,SAAS,UAAS;AACtC;AAEA,IAAI,sBAAsB;AAOV,SAAA,qBACdA,OACA,mBAA0B;AAE1B,MACE,OAAO,WAAW,eAClB,OAAO,aAAa,eACpB,CAAC,mBAAmB,OAAO,SAAS,IAAI,KACxC,eAAeA,KAAI,MAAM,qBACzB,eAAeA,KAAI;EACnB,qBACA;AACA;EACD;AAED,iBAAeA,KAAI,IAAI;AAEvB,WAAS,WAAW,IAAU;AAC5B,WAAO,uBAAuB,EAAE;EACjC;AACD,QAAM,WAAW;AACjB,QAAM,UAAU,mBAAkB;AAClC,QAAM,YAAY,QAAQ,KAAK,SAAS;AAExC,WAAS,WAAQ;AACf,UAAM,UAAU,SAAS,eAAe,QAAQ;AAChD,QAAI,SAAS;AACX,cAAQ,OAAM;IACf;EACF;AAED,WAAS,kBAAkB,UAAqB;AAC9C,aAAS,MAAM,UAAU;AACzB,aAAS,MAAM,aAAa;AAC5B,aAAS,MAAM,WAAW;AAC1B,aAAS,MAAM,SAAS;AACxB,aAAS,MAAM,OAAO;AACtB,aAAS,MAAM,UAAU;AACzB,aAAS,MAAM,eAAe;AAC9B,aAAS,MAAM,aAAa;EAC7B;AAED,WAAS,gBAAgB,aAAyB,QAAc;AAC9D,gBAAY,aAAa,SAAS,IAAI;AACtC,gBAAY,aAAa,MAAM,MAAM;AACrC,gBAAY,aAAa,UAAU,IAAI;AACvC,gBAAY,aAAa,WAAW,WAAW;AAC/C,gBAAY,aAAa,QAAQ,MAAM;AACvC,gBAAY,MAAM,aAAa;EAChC;AAED,WAAS,gBAAa;AACpB,UAAM,WAAW,SAAS,cAAc,MAAM;AAC9C,aAAS,MAAM,SAAS;AACxB,aAAS,MAAM,aAAa;AAC5B,aAAS,MAAM,WAAW;AAC1B,aAAS,YAAY;AACrB,aAAS,UAAU,MAAK;AACtB,4BAAsB;AACtB,eAAQ;IACV;AACA,WAAO;EACR;AAED,WAAS,gBACP,eACA,aAAmB;AAEnB,kBAAc,aAAa,MAAM,WAAW;AAC5C,kBAAc,YAAY;AAC1B,kBAAc,OACZ;AACF,kBAAc,aAAa,UAAU,SAAS;AAC9C,kBAAc,MAAM,cAAc;AAClC,kBAAc,MAAM,iBAAiB;EACtC;AAED,WAAS,WAAQ;AACf,UAAM,SAAS,cAAc,QAAQ;AACrC,UAAM,iBAAiB,WAAW,MAAM;AACxC,UAAM,eACJ,SAAS,eAAe,cAAc,KAAK,SAAS,cAAc,MAAM;AAC1E,UAAM,cAAc,WAAW,WAAW;AAC1C,UAAM,gBACH,SAAS,eAAe,WAAW,KACpC,SAAS,cAAc,GAAG;AAC5B,UAAM,gBAAgB,WAAW,cAAc;AAC/C,UAAM,cACH,SAAS,eACR,aAAa,KAEf,SAAS,gBAAgB,8BAA8B,KAAK;AAC9D,QAAI,OAAO,SAAS;AAElB,YAAM,WAAW,OAAO;AACxB,wBAAkB,QAAQ;AAC1B,sBAAgB,eAAe,WAAW;AAC1C,YAAM,WAAW,cAAa;AAC9B,sBAAgB,aAAa,aAAa;AAC1C,eAAS,OAAO,aAAa,cAAc,eAAe,QAAQ;AAClE,eAAS,KAAK,YAAY,QAAQ;IACnC;AAED,QAAI,WAAW;AACb,mBAAa,YAAY;AACzB,kBAAY,YAAY;;;;;;;;IAQzB,OAAM;AACL,kBAAY,YAAY;;;;;;;;AAQxB,mBAAa,YAAY;IAC1B;AACD,iBAAa,aAAa,MAAM,cAAc;EAC/C;AACD,MAAI,SAAS,eAAe,WAAW;AACrC,WAAO,iBAAiB,oBAAoB,QAAQ;EACrD,OAAM;AACL,aAAQ;EACT;AACH;SClSgB,QAAK;AACnB,MACE,OAAO,cAAc,eACrB,OAAO,UAAU,WAAW,MAAM,UAClC;AACA,WAAO,UAAU,WAAW;EAC7B,OAAM;AACL,WAAO;EACR;AACH;SASgB,kBAAe;AAC7B,SACE,OAAO,WAAW;;EAGlB,CAAC,EAAE,OAAO,SAAS,KAAK,OAAO,UAAU,KAAK,OAAO,UAAU,MAC/D,oDAAoD,KAAK,MAAK,CAAE;AAEpE;SAQgB,SAAM;;AACpB,QAAM,oBAAmB,iBAAa,MAAb,mBAAe;AACxC,MAAI,qBAAqB,QAAQ;AAC/B,WAAO;EACR,WAAU,qBAAqB,WAAW;AACzC,WAAO;EACR;AAED,MAAI;AACF,WACE,OAAO,UAAU,SAAS,KAAK,OAAO,OAAO,MAAM;EAEtD,SAAQ,GAAG;AACV,WAAO;EACR;AACH;SAQgB,YAAS;AACvB,SAAO,OAAO,WAAW,eAAe,YAAW;AACrD;SAKgB,cAAW;AACzB,SACE,OAAO,sBAAsB,eAC7B,OAAO,SAAS,eAChB,gBAAgB;AAEpB;SAKgB,qBAAkB;AAChC,SACE,OAAO,cAAc,eACrB,UAAU,cAAc;AAE5B;SAUgB,qBAAkB;AAChC,QAAM,UACJ,OAAO,WAAW,WACd,OAAO,UACP,OAAO,YAAY,WACnB,QAAQ,UACR;AACN,SAAO,OAAO,YAAY,YAAY,QAAQ,OAAO;AACvD;SAOgB,gBAAa;AAC3B,SACE,OAAO,cAAc,YAAY,UAAU,SAAS,MAAM;AAE9D;SAQgB,OAAI;AAClB,QAAM,KAAK,MAAK;AAChB,SAAO,GAAG,QAAQ,OAAO,KAAK,KAAK,GAAG,QAAQ,UAAU,KAAK;AAC/D;SAiBgB,WAAQ;AACtB,SACE,CAAC,OAAQ,KACT,CAAC,CAAC,UAAU,aACZ,UAAU,UAAU,SAAS,QAAQ,KACrC,CAAC,UAAU,UAAU,SAAS,QAAQ;AAE1C;SAGgB,mBAAgB;AAC9B,SACE,CAAC,OAAQ,KACT,CAAC,CAAC,UAAU,cACX,UAAU,UAAU,SAAS,QAAQ,KACpC,UAAU,UAAU,SAAS,QAAQ,MACvC,CAAC,UAAU,UAAU,SAAS,QAAQ;AAE1C;SAMgB,uBAAoB;AAClC,MAAI;AACF,WAAO,OAAO,cAAc;EAC7B,SAAQ,GAAG;AACV,WAAO;EACR;AACH;SASgB,4BAAyB;AACvC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,QAAI;AACF,UAAI,WAAoB;AACxB,YAAM,gBACJ;AACF,YAAM,UAAU,KAAK,UAAU,KAAK,aAAa;AACjD,cAAQ,YAAY,MAAK;AACvB,gBAAQ,OAAO,MAAK;AAEpB,YAAI,CAAC,UAAU;AACb,eAAK,UAAU,eAAe,aAAa;QAC5C;AACD,gBAAQ,IAAI;MACd;AACA,cAAQ,kBAAkB,MAAK;AAC7B,mBAAW;MACb;AAEA,cAAQ,UAAU,MAAK;;AACrB,iBAAO,aAAQ,UAAR,mBAAe,YAAW,EAAE;MACrC;IACD,SAAQ,OAAO;AACd,aAAO,KAAK;IACb;EACH,CAAC;AACH;AC1KA,IAAM,aAAa;AAYb,IAAO,gBAAP,MAAO,uBAAsB,MAAK;EAItC,YAEW,MACT,SAEO,YAAoC;AAE3C,UAAM,OAAO;AALJ,SAAI,OAAJ;AAGF,SAAU,aAAV;AAPA,SAAI,OAAW;AAetB,WAAO,eAAe,MAAM,eAAc,SAAS;AAInD,QAAI,MAAM,mBAAmB;AAC3B,YAAM,kBAAkB,MAAM,aAAa,UAAU,MAAM;IAC5D;EACF;AACF;IAEY,qBAAY;EAIvB,YACmB,SACA,aACA,QAA2B;AAF3B,SAAO,UAAP;AACA,SAAW,cAAX;AACA,SAAM,SAAN;EACf;EAEJ,OACE,SACG,MAAyD;AAE5D,UAAM,aAAc,KAAK,CAAC,KAAmB,CAAA;AAC7C,UAAM,WAAW,GAAG,KAAK,OAAO,IAAI,IAAI;AACxC,UAAM,WAAW,KAAK,OAAO,IAAI;AAEjC,UAAM,UAAU,WAAW,gBAAgB,UAAU,UAAU,IAAI;AAEnE,UAAM,cAAc,GAAG,KAAK,WAAW,KAAK,OAAO,KAAK,QAAQ;AAEhE,UAAM,QAAQ,IAAI,cAAc,UAAU,aAAa,UAAU;AAEjE,WAAO;EACR;AACF;AAED,SAAS,gBAAgB,UAAkB,MAAe;AACxD,SAAO,SAAS,QAAQ,SAAS,CAAC,GAAG,QAAO;AAC1C,UAAM,QAAQ,KAAK,GAAG;AACtB,WAAO,SAAS,OAAO,OAAO,KAAK,IAAI,IAAI,GAAG;EAChD,CAAC;AACH;AAEA,IAAM,UAAU;AGvGV,SAAU,QAAQ,KAAW;AACjC,aAAW,OAAO,KAAK;AACrB,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAClD,aAAO;IACR;EACF;AACD,SAAO;AACT;AAmBgB,SAAA,UAAU,GAAW,GAAS;AAC5C,MAAI,MAAM,GAAG;AACX,WAAO;EACR;AAED,QAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,QAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,aAAW,KAAK,OAAO;AACrB,QAAI,CAAC,MAAM,SAAS,CAAC,GAAG;AACtB,aAAO;IACR;AAED,UAAM,QAAS,EAA8B,CAAC;AAC9C,UAAM,QAAS,EAA8B,CAAC;AAC9C,QAAI,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AACtC,UAAI,CAAC,UAAU,OAAO,KAAK,GAAG;AAC5B,eAAO;MACR;IACF,WAAU,UAAU,OAAO;AAC1B,aAAO;IACR;EACF;AAED,aAAW,KAAK,OAAO;AACrB,QAAI,CAAC,MAAM,SAAS,CAAC,GAAG;AACtB,aAAO;IACR;EACF;AACD,SAAO;AACT;AAEA,SAAS,SAAS,OAAc;AAC9B,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC5C;AErEM,SAAU,YAAY,mBAE3B;AACC,QAAM,SAAS,CAAA;AACf,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,iBAAiB,GAAG;AAC5D,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,YAAM,QAAQ,cAAW;AACvB,eAAO,KACL,mBAAmB,GAAG,IAAI,MAAM,mBAAmB,QAAQ,CAAC;MAEhE,CAAC;IACF,OAAM;AACL,aAAO,KAAK,mBAAmB,GAAG,IAAI,MAAM,mBAAmB,KAAK,CAAC;IACtE;EACF;AACD,SAAO,OAAO,SAAS,MAAM,OAAO,KAAK,GAAG,IAAI;AAClD;AAMM,SAAU,kBAAkBC,cAAmB;AACnD,QAAM,MAA8B,CAAA;AACpC,QAAM,SAASA,aAAY,QAAQ,OAAO,EAAE,EAAE,MAAM,GAAG;AAEvD,SAAO,QAAQ,WAAQ;AACrB,QAAI,OAAO;AACT,YAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,UAAI,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,KAAK;IACxD;EACH,CAAC;AACD,SAAO;AACT;AAKM,SAAU,mBAAmB,KAAW;AAC5C,QAAM,aAAa,IAAI,QAAQ,GAAG;AAClC,MAAI,CAAC,YAAY;AACf,WAAO;EACR;AACD,QAAM,gBAAgB,IAAI,QAAQ,KAAK,UAAU;AACjD,SAAO,IAAI,UACT,YACA,gBAAgB,IAAI,gBAAgB,MAAS;AAEjD;AEVgB,SAAA,gBACd,UACA,eAA2B;AAE3B,QAAM,QAAQ,IAAI,cAAiB,UAAU,aAAa;AAC1D,SAAO,MAAM,UAAU,KAAK,KAAK;AACnC;AAMA,IAAM,gBAAN,MAAmB;;;;;;EAejB,YAAY,UAAuB,eAA2B;AAdtD,SAAS,YAAmC,CAAA;AAC5C,SAAY,eAAkB,CAAA;AAE9B,SAAa,gBAAG;AAEhB,SAAA,OAAO,QAAQ,QAAO;AACtB,SAAS,YAAG;AASlB,SAAK,gBAAgB;AAIrB,SAAK,KACF,KAAK,MAAK;AACT,eAAS,IAAI;IACf,CAAC,EACA,MAAM,OAAI;AACT,WAAK,MAAM,CAAC;IACd,CAAC;EACJ;EAED,KAAK,OAAQ;AACX,SAAK,gBAAgB,CAAC,aAAyB;AAC7C,eAAS,KAAK,KAAK;IACrB,CAAC;EACF;EAED,MAAM,OAAY;AAChB,SAAK,gBAAgB,CAAC,aAAyB;AAC7C,eAAS,MAAM,KAAK;IACtB,CAAC;AACD,SAAK,MAAM,KAAK;EACjB;EAED,WAAQ;AACN,SAAK,gBAAgB,CAAC,aAAyB;AAC7C,eAAS,SAAQ;IACnB,CAAC;AACD,SAAK,MAAK;EACX;;;;;;;EAQD,UACE,gBACA,OACA,UAAqB;AAErB,QAAI;AAEJ,QACE,mBAAmB,UACnB,UAAU,UACV,aAAa,QACb;AACA,YAAM,IAAI,MAAM,mBAAmB;IACpC;AAGD,QACE,qBAAqB,gBAA8C;MACjE;MACA;MACA;IACD,CAAA,GACD;AACA,iBAAW;IACZ,OAAM;AACL,iBAAW;QACT,MAAM;QACN;QACA;;IAEH;AAED,QAAI,SAAS,SAAS,QAAW;AAC/B,eAAS,OAAO;IACjB;AACD,QAAI,SAAS,UAAU,QAAW;AAChC,eAAS,QAAQ;IAClB;AACD,QAAI,SAAS,aAAa,QAAW;AACnC,eAAS,WAAW;IACrB;AAED,UAAM,QAAQ,KAAK,eAAe,KAAK,MAAM,KAAK,UAAW,MAAM;AAKnE,QAAI,KAAK,WAAW;AAElB,WAAK,KAAK,KAAK,MAAK;AAClB,YAAI;AACF,cAAI,KAAK,YAAY;AACnB,qBAAS,MAAM,KAAK,UAAU;UAC/B,OAAM;AACL,qBAAS,SAAQ;UAClB;QACF,SAAQ,GAAG;QAEX;AACD;MACF,CAAC;IACF;AAED,SAAK,UAAW,KAAK,QAAuB;AAE5C,WAAO;EACR;;;EAIO,eAAe,GAAS;AAC9B,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,CAAC,MAAM,QAAW;AACnE;IACD;AAED,WAAO,KAAK,UAAU,CAAC;AAEvB,SAAK,iBAAiB;AACtB,QAAI,KAAK,kBAAkB,KAAK,KAAK,kBAAkB,QAAW;AAChE,WAAK,cAAc,IAAI;IACxB;EACF;EAEO,gBAAgB,IAAmC;AACzD,QAAI,KAAK,WAAW;AAElB;IACD;AAID,aAAS,IAAI,GAAG,IAAI,KAAK,UAAW,QAAQ,KAAK;AAC/C,WAAK,QAAQ,GAAG,EAAE;IACnB;EACF;;;;EAKO,QAAQ,GAAW,IAAmC;AAG5D,SAAK,KAAK,KAAK,MAAK;AAClB,UAAI,KAAK,cAAc,UAAa,KAAK,UAAU,CAAC,MAAM,QAAW;AACnE,YAAI;AACF,aAAG,KAAK,UAAU,CAAC,CAAC;QACrB,SAAQ,GAAG;AAIV,cAAI,OAAO,YAAY,eAAe,QAAQ,OAAO;AACnD,oBAAQ,MAAM,CAAC;UAChB;QACF;MACF;IACH,CAAC;EACF;EAEO,MAAM,KAAW;AACvB,QAAI,KAAK,WAAW;AAClB;IACD;AACD,SAAK,YAAY;AACjB,QAAI,QAAQ,QAAW;AACrB,WAAK,aAAa;IACnB;AAGD,SAAK,KAAK,KAAK,MAAK;AAClB,WAAK,YAAY;AACjB,WAAK,gBAAgB;IACvB,CAAC;EACF;AACF;AAqBD,SAAS,qBACP,KACA,SAAiB;AAEjB,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,WAAO;EACR;AAED,aAAW,UAAU,SAAS;AAC5B,QAAI,UAAU,OAAO,OAAO,IAAI,MAAM,MAAM,YAAY;AACtD,aAAO;IACR;EACF;AAED,SAAO;AACT;AAEA,SAAS,OAAI;AAEb;AG3QO,IAAM,mBAAmB,IAAI,KAAK,KAAK;AEZxC,SAAU,mBACd,SAAwC;AAExC,MAAI,WAAY,QAA+B,WAAW;AACxD,WAAQ,QAA+B;EACxC,OAAM;AACL,WAAO;EACR;AACH;;;ICDa,kBAAS;;;;;;;EAiBpB,YACWC,OACA,iBACA,MAAmB;AAFnB,SAAI,OAAJA;AACA,SAAe,kBAAf;AACA,SAAI,OAAJ;AAnBX,SAAiB,oBAAG;AAIpB,SAAY,eAAe,CAAA;AAE3B,SAAA,oBAA2C;AAE3C,SAAiB,oBAAwC;;EAczD,qBAAqB,MAAuB;AAC1C,SAAK,oBAAoB;AACzB,WAAO;;EAGT,qBAAqB,mBAA0B;AAC7C,SAAK,oBAAoB;AACzB,WAAO;;EAGT,gBAAgB,OAAiB;AAC/B,SAAK,eAAe;AACpB,WAAO;;EAGT,2BAA2B,UAAsC;AAC/D,SAAK,oBAAoB;AACzB,WAAO;;AAEV;ACrDM,IAAM,qBAAqB;ICgBrB,iBAAQ;EAWnB,YACmBA,OACA,WAA6B;AAD7B,SAAI,OAAJA;AACA,SAAS,YAAT;AAZX,SAAS,YAAwB;AACxB,SAAA,YAAgD,oBAAI,IAAG;AACvD,SAAA,oBAGb,oBAAI,IAAG;AACM,SAAA,mBACf,oBAAI,IAAG;AACD,SAAA,kBAAuD,oBAAI,IAAG;;;;;;EAWtE,IAAI,YAAmB;AAErB,UAAM,uBAAuB,KAAK,4BAA4B,UAAU;AAExE,QAAI,CAAC,KAAK,kBAAkB,IAAI,oBAAoB,GAAG;AACrD,YAAM,WAAW,IAAI,SAAQ;AAC7B,WAAK,kBAAkB,IAAI,sBAAsB,QAAQ;AAEzD,UACE,KAAK,cAAc,oBAAoB,KACvC,KAAK,qBAAoB,GACzB;AAEA,YAAI;AACF,gBAAM,WAAW,KAAK,uBAAuB;YAC3C,oBAAoB;UACrB,CAAA;AACD,cAAI,UAAU;AACZ,qBAAS,QAAQ,QAAQ;;iBAEpB,GAAG;;;;AAOhB,WAAO,KAAK,kBAAkB,IAAI,oBAAoB,EAAG;;EAmB3D,aAAa,SAGZ;AAEC,UAAM,uBAAuB,KAAK,4BAChC,mCAAS,UAAU;AAErB,UAAM,YAAW,mCAAS,aAAY;AAEtC,QACE,KAAK,cAAc,oBAAoB,KACvC,KAAK,qBAAoB,GACzB;AACA,UAAI;AACF,eAAO,KAAK,uBAAuB;UACjC,oBAAoB;QACrB,CAAA;eACM,GAAG;AACV,YAAI,UAAU;AACZ,iBAAO;eACF;AACL,gBAAM;;;WAGL;AAEL,UAAI,UAAU;AACZ,eAAO;aACF;AACL,cAAM,MAAM,WAAW,KAAK,IAAI,mBAAmB;;;;EAKzD,eAAY;AACV,WAAO,KAAK;;EAGd,aAAa,WAAuB;AAClC,QAAI,UAAU,SAAS,KAAK,MAAM;AAChC,YAAM,MACJ,yBAAyB,UAAU,IAAI,iBAAiB,KAAK,IAAI,GAAG;;AAIxE,QAAI,KAAK,WAAW;AAClB,YAAM,MAAM,iBAAiB,KAAK,IAAI,4BAA4B;;AAGpE,SAAK,YAAY;AAGjB,QAAI,CAAC,KAAK,qBAAoB,GAAI;AAChC;;AAIF,QAAI,iBAAiB,SAAS,GAAG;AAC/B,UAAI;AACF,aAAK,uBAAuB,EAAE,oBAAoB,mBAAkB,CAAE;eAC/D,GAAG;;;AAWd,eAAW,CACT,oBACA,gBAAgB,KACb,KAAK,kBAAkB,QAAO,GAAI;AACrC,YAAM,uBACJ,KAAK,4BAA4B,kBAAkB;AAErD,UAAI;AAEF,cAAM,WAAW,KAAK,uBAAuB;UAC3C,oBAAoB;QACrB,CAAA;AACD,yBAAiB,QAAQ,QAAQ;eAC1B,GAAG;;;;EAOhB,cAAc,aAAqB,oBAAkB;AACnD,SAAK,kBAAkB,OAAO,UAAU;AACxC,SAAK,iBAAiB,OAAO,UAAU;AACvC,SAAK,UAAU,OAAO,UAAU;;;;EAKlC,MAAM,SAAM;AACV,UAAM,WAAW,MAAM,KAAK,KAAK,UAAU,OAAM,CAAE;AAEnD,UAAM,QAAQ,IAAI;MAChB,GAAG,SACA,OAAO,aAAW,cAAc,OAAO,EAEvC,IAAI,aAAY,QAAgB,SAAU,OAAM,CAAE;MACrD,GAAG,SACA,OAAO,aAAW,aAAa,OAAO,EAEtC,IAAI,aAAY,QAAgB,QAAO,CAAE;IAC7C,CAAA;;EAGH,iBAAc;AACZ,WAAO,KAAK,aAAa;;EAG3B,cAAc,aAAqB,oBAAkB;AACnD,WAAO,KAAK,UAAU,IAAI,UAAU;;EAGtC,WAAW,aAAqB,oBAAkB;AAChD,WAAO,KAAK,iBAAiB,IAAI,UAAU,KAAK,CAAA;;EAGlD,WAAW,OAA0B,CAAA,GAAE;AACrC,UAAM,EAAE,UAAU,CAAA,EAAE,IAAK;AACzB,UAAM,uBAAuB,KAAK,4BAChC,KAAK,kBAAkB;AAEzB,QAAI,KAAK,cAAc,oBAAoB,GAAG;AAC5C,YAAM,MACJ,GAAG,KAAK,IAAI,IAAI,oBAAoB,gCAAgC;;AAIxE,QAAI,CAAC,KAAK,eAAc,GAAI;AAC1B,YAAM,MAAM,aAAa,KAAK,IAAI,8BAA8B;;AAGlE,UAAM,WAAW,KAAK,uBAAuB;MAC3C,oBAAoB;MACpB;IACD,CAAA;AAGD,eAAW,CACT,oBACA,gBAAgB,KACb,KAAK,kBAAkB,QAAO,GAAI;AACrC,YAAM,+BACJ,KAAK,4BAA4B,kBAAkB;AACrD,UAAI,yBAAyB,8BAA8B;AACzD,yBAAiB,QAAQ,QAAQ;;;AAIrC,WAAO;;;;;;;;;;EAWT,OAAO,UAA6B,YAAmB;AACrD,UAAM,uBAAuB,KAAK,4BAA4B,UAAU;AACxE,UAAM,oBACJ,KAAK,gBAAgB,IAAI,oBAAoB,KAC7C,oBAAI,IAAG;AACT,sBAAkB,IAAI,QAAQ;AAC9B,SAAK,gBAAgB,IAAI,sBAAsB,iBAAiB;AAEhE,UAAM,mBAAmB,KAAK,UAAU,IAAI,oBAAoB;AAChE,QAAI,kBAAkB;AACpB,eAAS,kBAAkB,oBAAoB;;AAGjD,WAAO,MAAK;AACV,wBAAkB,OAAO,QAAQ;IACnC;;;;;;EAOM,sBACN,UACA,YAAkB;AAElB,UAAM,YAAY,KAAK,gBAAgB,IAAI,UAAU;AACrD,QAAI,CAAC,WAAW;AACd;;AAEF,eAAW,YAAY,WAAW;AAChC,UAAI;AACF,iBAAS,UAAU,UAAU;cACvB;;;;EAMJ,uBAAuB,EAC7B,oBACA,UAAU,CAAA,EAAE,GAIb;AACC,QAAI,WAAW,KAAK,UAAU,IAAI,kBAAkB;AACpD,QAAI,CAAC,YAAY,KAAK,WAAW;AAC/B,iBAAW,KAAK,UAAU,gBAAgB,KAAK,WAAW;QACxD,oBAAoB,8BAA8B,kBAAkB;QACpE;MACD,CAAA;AACD,WAAK,UAAU,IAAI,oBAAoB,QAAS;AAChD,WAAK,iBAAiB,IAAI,oBAAoB,OAAO;AAOrD,WAAK,sBAAsB,UAAW,kBAAkB;AAOxD,UAAI,KAAK,UAAU,mBAAmB;AACpC,YAAI;AACF,eAAK,UAAU,kBACb,KAAK,WACL,oBACA,QAAS;gBAEL;;;;AAMZ,WAAO,YAAY;;EAGb,4BACN,aAAqB,oBAAkB;AAEvC,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,oBAAoB,aAAa;WAClD;AACL,aAAO;;;EAIH,uBAAoB;AAC1B,WACE,CAAC,CAAC,KAAK,aACP,KAAK,UAAU,sBAAiB;;AAGrC;AAGD,SAAS,8BAA8B,YAAkB;AACvD,SAAO,eAAe,qBAAqB,SAAY;AACzD;AAEA,SAAS,iBAAiC,WAAuB;AAC/D,SAAO,UAAU,sBAAiB;AACpC;ICjWa,2BAAkB;EAG7B,YAA6BA,OAAY;AAAZ,SAAI,OAAJA;AAFZ,SAAA,YAAY,oBAAI,IAAG;;;;;;;;;;;EAapC,aAA6B,WAAuB;AAClD,UAAM,WAAW,KAAK,YAAY,UAAU,IAAI;AAChD,QAAI,SAAS,eAAc,GAAI;AAC7B,YAAM,IAAI,MACR,aAAa,UAAU,IAAI,qCAAqC,KAAK,IAAI,EAAE;;AAI/E,aAAS,aAAa,SAAS;;EAGjC,wBAAwC,WAAuB;AAC7D,UAAM,WAAW,KAAK,YAAY,UAAU,IAAI;AAChD,QAAI,SAAS,eAAc,GAAI;AAE7B,WAAK,UAAU,OAAO,UAAU,IAAI;;AAGtC,SAAK,aAAa,SAAS;;;;;;;;;EAU7B,YAA4BA,OAAO;AACjC,QAAI,KAAK,UAAU,IAAIA,KAAI,GAAG;AAC5B,aAAO,KAAK,UAAU,IAAIA,KAAI;;AAIhC,UAAM,WAAW,IAAI,SAAYA,OAAM,IAAI;AAC3C,SAAK,UAAU,IAAIA,OAAM,QAAqC;AAE9D,WAAO;;EAGT,eAAY;AACV,WAAO,MAAM,KAAK,KAAK,UAAU,OAAM,CAAE;;AAE5C;;;ACxCM,IAAM,YAAsB,CAAA;IAavB;CAAZ,SAAYC,WAAQ;AAClB,EAAAA,UAAAA,UAAA,OAAA,IAAA,CAAA,IAAA;AACA,EAAAA,UAAAA,UAAA,SAAA,IAAA,CAAA,IAAA;AACA,EAAAA,UAAAA,UAAA,MAAA,IAAA,CAAA,IAAA;AACA,EAAAA,UAAAA,UAAA,MAAA,IAAA,CAAA,IAAA;AACA,EAAAA,UAAAA,UAAA,OAAA,IAAA,CAAA,IAAA;AACA,EAAAA,UAAAA,UAAA,QAAA,IAAA,CAAA,IAAA;AACF,GAPY,aAAA,WAOX,CAAA,EAAA;AAED,IAAM,oBAA2D;EAC/D,SAAS,SAAS;EAClB,WAAW,SAAS;EACpB,QAAQ,SAAS;EACjB,QAAQ,SAAS;EACjB,SAAS,SAAS;EAClB,UAAU,SAAS;;AAMrB,IAAM,kBAA4B,SAAS;AAmB3C,IAAM,gBAAgB;EACpB,CAAC,SAAS,KAAK,GAAG;EAClB,CAAC,SAAS,OAAO,GAAG;EACpB,CAAC,SAAS,IAAI,GAAG;EACjB,CAAC,SAAS,IAAI,GAAG;EACjB,CAAC,SAAS,KAAK,GAAG;;AAQpB,IAAM,oBAAgC,CAAC,UAAU,YAAY,SAAc;AACzE,MAAI,UAAU,SAAS,UAAU;AAC/B;;AAEF,QAAM,OAAM,oBAAI,KAAI,GAAG,YAAW;AAClC,QAAM,SAAS,cAAc,OAAqC;AAClE,MAAI,QAAQ;AACV,YAAQ,MAA2C,EACjD,IAAI,GAAG,MAAM,SAAS,IAAI,KAC1B,GAAG,IAAI;SAEJ;AACL,UAAM,IAAI,MACR,8DAA8D,OAAO,GAAG;;AAG9E;IAEa,eAAM;;;;;;;EAOjB,YAAmBC,OAAY;AAAZ,SAAI,OAAJA;AAUX,SAAS,YAAG;AAsBZ,SAAW,cAAe;AAc1B,SAAe,kBAAsB;AA1C3C,cAAU,KAAK,IAAI;;EAQrB,IAAI,WAAQ;AACV,WAAO,KAAK;;EAGd,IAAI,SAAS,KAAa;AACxB,QAAI,EAAE,OAAO,WAAW;AACtB,YAAM,IAAI,UAAU,kBAAkB,GAAG,4BAA4B;;AAEvE,SAAK,YAAY;;;EAInB,YAAY,KAA8B;AACxC,SAAK,YAAY,OAAO,QAAQ,WAAW,kBAAkB,GAAG,IAAI;;EAQtE,IAAI,aAAU;AACZ,WAAO,KAAK;;EAEd,IAAI,WAAW,KAAe;AAC5B,QAAI,OAAO,QAAQ,YAAY;AAC7B,YAAM,IAAI,UAAU,mDAAmD;;AAEzE,SAAK,cAAc;;EAOrB,IAAI,iBAAc;AAChB,WAAO,KAAK;;EAEd,IAAI,eAAe,KAAsB;AACvC,SAAK,kBAAkB;;;;;EAOzB,SAAS,MAAe;AACtB,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,OAAO,GAAG,IAAI;AAC1E,SAAK,YAAY,MAAM,SAAS,OAAO,GAAG,IAAI;;EAEhD,OAAO,MAAe;AACpB,SAAK,mBACH,KAAK,gBAAgB,MAAM,SAAS,SAAS,GAAG,IAAI;AACtD,SAAK,YAAY,MAAM,SAAS,SAAS,GAAG,IAAI;;EAElD,QAAQ,MAAe;AACrB,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,MAAM,GAAG,IAAI;AACzE,SAAK,YAAY,MAAM,SAAS,MAAM,GAAG,IAAI;;EAE/C,QAAQ,MAAe;AACrB,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,MAAM,GAAG,IAAI;AACzE,SAAK,YAAY,MAAM,SAAS,MAAM,GAAG,IAAI;;EAE/C,SAAS,MAAe;AACtB,SAAK,mBAAmB,KAAK,gBAAgB,MAAM,SAAS,OAAO,GAAG,IAAI;AAC1E,SAAK,YAAY,MAAM,SAAS,OAAO,GAAG,IAAI;;AAEjD;AAEK,SAAU,YAAY,OAAgC;AAC1D,YAAU,QAAQ,UAAO;AACvB,SAAK,YAAY,KAAK;EACxB,CAAC;AACH;AAEgB,SAAA,kBACd,aACA,SAAoB;AAEpB,aAAW,YAAY,WAAW;AAChC,QAAI,iBAAkC;AACtC,QAAI,WAAW,QAAQ,OAAO;AAC5B,uBAAiB,kBAAkB,QAAQ,KAAK;;AAElD,QAAI,gBAAgB,MAAM;AACxB,eAAS,iBAAiB;WACrB;AACL,eAAS,iBAAiB,CACxBC,WACA,UACG,SACD;AACF,cAAM,UAAU,KACb,IAAI,SAAM;AACT,cAAI,OAAO,MAAM;AACf,mBAAO;qBACE,OAAO,QAAQ,UAAU;AAClC,mBAAO;qBACE,OAAO,QAAQ,YAAY,OAAO,QAAQ,WAAW;AAC9D,mBAAO,IAAI,SAAQ;qBACV,eAAe,OAAO;AAC/B,mBAAO,IAAI;iBACN;AACL,gBAAI;AACF,qBAAO,KAAK,UAAU,GAAG;qBAClB,SAAS;AAChB,qBAAO;;;QAGb,CAAC,EACA,OAAO,SAAO,GAAG,EACjB,KAAK,GAAG;AACX,YAAI,UAAU,kBAAkBA,UAAS,WAAW;AAClD,sBAAY;YACV,OAAO,SAAS,KAAK,EAAE,YAAW;YAClC;YACA;YACA,MAAMA,UAAS;UAChB,CAAA;;MAEL;;;AAGN;;;AC3QA,IAAM,gBAAgB,CAAC,QAAQ,iBAAiB,aAAa,KAAK,CAAC,MAAM,kBAAkB,CAAC;AAE5F,IAAI;AACJ,IAAI;AAEJ,SAAS,uBAAuB;AAC5B,SAAQ,sBACH,oBAAoB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACR;AAEA,SAAS,0BAA0B;AAC/B,SAAQ,yBACH,uBAAuB;AAAA,IACpB,UAAU,UAAU;AAAA,IACpB,UAAU,UAAU;AAAA,IACpB,UAAU,UAAU;AAAA,EACxB;AACR;AACA,IAAM,mBAAmB,oBAAI,QAAQ;AACrC,IAAM,qBAAqB,oBAAI,QAAQ;AACvC,IAAM,2BAA2B,oBAAI,QAAQ;AAC7C,IAAM,iBAAiB,oBAAI,QAAQ;AACnC,IAAM,wBAAwB,oBAAI,QAAQ;AAC1C,SAAS,iBAAiB,SAAS;AAC/B,QAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7C,UAAM,WAAW,MAAM;AACnB,cAAQ,oBAAoB,WAAW,OAAO;AAC9C,cAAQ,oBAAoB,SAAS,KAAK;AAAA,IAC9C;AACA,UAAM,UAAU,MAAM;AAClB,cAAQ,KAAK,QAAQ,MAAM,CAAC;AAC5B,eAAS;AAAA,IACb;AACA,UAAM,QAAQ,MAAM;AAChB,aAAO,QAAQ,KAAK;AACpB,eAAS;AAAA,IACb;AACA,YAAQ,iBAAiB,WAAW,OAAO;AAC3C,YAAQ,iBAAiB,SAAS,KAAK;AAAA,EAC3C,CAAC;AACD,UACK,KAAK,CAAC,UAAU;AAGjB,QAAI,iBAAiB,WAAW;AAC5B,uBAAiB,IAAI,OAAO,OAAO;AAAA,IACvC;AAAA,EAEJ,CAAC,EACI,MAAM,MAAM;AAAA,EAAE,CAAC;AAGpB,wBAAsB,IAAI,SAAS,OAAO;AAC1C,SAAO;AACX;AACA,SAAS,+BAA+B,IAAI;AAExC,MAAI,mBAAmB,IAAI,EAAE;AACzB;AACJ,QAAM,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1C,UAAM,WAAW,MAAM;AACnB,SAAG,oBAAoB,YAAY,QAAQ;AAC3C,SAAG,oBAAoB,SAAS,KAAK;AACrC,SAAG,oBAAoB,SAAS,KAAK;AAAA,IACzC;AACA,UAAM,WAAW,MAAM;AACnB,cAAQ;AACR,eAAS;AAAA,IACb;AACA,UAAM,QAAQ,MAAM;AAChB,aAAO,GAAG,SAAS,IAAI,aAAa,cAAc,YAAY,CAAC;AAC/D,eAAS;AAAA,IACb;AACA,OAAG,iBAAiB,YAAY,QAAQ;AACxC,OAAG,iBAAiB,SAAS,KAAK;AAClC,OAAG,iBAAiB,SAAS,KAAK;AAAA,EACtC,CAAC;AAED,qBAAmB,IAAI,IAAI,IAAI;AACnC;AACA,IAAI,gBAAgB;AAAA,EAChB,IAAI,QAAQ,MAAM,UAAU;AACxB,QAAI,kBAAkB,gBAAgB;AAElC,UAAI,SAAS;AACT,eAAO,mBAAmB,IAAI,MAAM;AAExC,UAAI,SAAS,oBAAoB;AAC7B,eAAO,OAAO,oBAAoB,yBAAyB,IAAI,MAAM;AAAA,MACzE;AAEA,UAAI,SAAS,SAAS;AAClB,eAAO,SAAS,iBAAiB,CAAC,IAC5B,SACA,SAAS,YAAY,SAAS,iBAAiB,CAAC,CAAC;AAAA,MAC3D;AAAA,IACJ;AAEA,WAAO,KAAK,OAAO,IAAI,CAAC;AAAA,EAC5B;AAAA,EACA,IAAI,QAAQ,MAAM,OAAO;AACrB,WAAO,IAAI,IAAI;AACf,WAAO;AAAA,EACX;AAAA,EACA,IAAI,QAAQ,MAAM;AACd,QAAI,kBAAkB,mBACjB,SAAS,UAAU,SAAS,UAAU;AACvC,aAAO;AAAA,IACX;AACA,WAAO,QAAQ;AAAA,EACnB;AACJ;AACA,SAAS,aAAa,UAAU;AAC5B,kBAAgB,SAAS,aAAa;AAC1C;AACA,SAAS,aAAa,MAAM;AAIxB,MAAI,SAAS,YAAY,UAAU,eAC/B,EAAE,sBAAsB,eAAe,YAAY;AACnD,WAAO,SAAU,eAAe,MAAM;AAClC,YAAM,KAAK,KAAK,KAAK,OAAO,IAAI,GAAG,YAAY,GAAG,IAAI;AACtD,+BAAyB,IAAI,IAAI,WAAW,OAAO,WAAW,KAAK,IAAI,CAAC,UAAU,CAAC;AACnF,aAAO,KAAK,EAAE;AAAA,IAClB;AAAA,EACJ;AAMA,MAAI,wBAAwB,EAAE,SAAS,IAAI,GAAG;AAC1C,WAAO,YAAa,MAAM;AAGtB,WAAK,MAAM,OAAO,IAAI,GAAG,IAAI;AAC7B,aAAO,KAAK,iBAAiB,IAAI,IAAI,CAAC;AAAA,IAC1C;AAAA,EACJ;AACA,SAAO,YAAa,MAAM;AAGtB,WAAO,KAAK,KAAK,MAAM,OAAO,IAAI,GAAG,IAAI,CAAC;AAAA,EAC9C;AACJ;AACA,SAAS,uBAAuB,OAAO;AACnC,MAAI,OAAO,UAAU;AACjB,WAAO,aAAa,KAAK;AAG7B,MAAI,iBAAiB;AACjB,mCAA+B,KAAK;AACxC,MAAI,cAAc,OAAO,qBAAqB,CAAC;AAC3C,WAAO,IAAI,MAAM,OAAO,aAAa;AAEzC,SAAO;AACX;AACA,SAAS,KAAK,OAAO;AAGjB,MAAI,iBAAiB;AACjB,WAAO,iBAAiB,KAAK;AAGjC,MAAI,eAAe,IAAI,KAAK;AACxB,WAAO,eAAe,IAAI,KAAK;AACnC,QAAM,WAAW,uBAAuB,KAAK;AAG7C,MAAI,aAAa,OAAO;AACpB,mBAAe,IAAI,OAAO,QAAQ;AAClC,0BAAsB,IAAI,UAAU,KAAK;AAAA,EAC7C;AACA,SAAO;AACX;AACA,IAAM,SAAS,CAAC,UAAU,sBAAsB,IAAI,KAAK;;;AC5KzD,SAAS,OAAOC,OAAMC,UAAS,EAAE,SAAS,SAAS,UAAU,WAAW,IAAI,CAAC,GAAG;AAC5E,QAAM,UAAU,UAAU,KAAKD,OAAMC,QAAO;AAC5C,QAAM,cAAc,KAAK,OAAO;AAChC,MAAI,SAAS;AACT,YAAQ,iBAAiB,iBAAiB,CAAC,UAAU;AACjD,cAAQ,KAAK,QAAQ,MAAM,GAAG,MAAM,YAAY,MAAM,YAAY,KAAK,QAAQ,WAAW,GAAG,KAAK;AAAA,IACtG,CAAC;AAAA,EACL;AACA,MAAI,SAAS;AACT,YAAQ,iBAAiB,WAAW,CAAC,UAAU;AAAA;AAAA,MAE/C,MAAM;AAAA,MAAY,MAAM;AAAA,MAAY;AAAA,IAAK,CAAC;AAAA,EAC9C;AACA,cACK,KAAK,CAAC,OAAO;AACd,QAAI;AACA,SAAG,iBAAiB,SAAS,MAAM,WAAW,CAAC;AACnD,QAAI,UAAU;AACV,SAAG,iBAAiB,iBAAiB,CAAC,UAAU,SAAS,MAAM,YAAY,MAAM,YAAY,KAAK,CAAC;AAAA,IACvG;AAAA,EACJ,CAAC,EACI,MAAM,MAAM;AAAA,EAAE,CAAC;AACpB,SAAO;AACX;AAgBA,IAAM,cAAc,CAAC,OAAO,UAAU,UAAU,cAAc,OAAO;AACrE,IAAM,eAAe,CAAC,OAAO,OAAO,UAAU,OAAO;AACrD,IAAM,gBAAgB,oBAAI,IAAI;AAC9B,SAAS,UAAU,QAAQ,MAAM;AAC7B,MAAI,EAAE,kBAAkB,eACpB,EAAE,QAAQ,WACV,OAAO,SAAS,WAAW;AAC3B;AAAA,EACJ;AACA,MAAI,cAAc,IAAI,IAAI;AACtB,WAAO,cAAc,IAAI,IAAI;AACjC,QAAM,iBAAiB,KAAK,QAAQ,cAAc,EAAE;AACpD,QAAM,WAAW,SAAS;AAC1B,QAAM,UAAU,aAAa,SAAS,cAAc;AACpD;AAAA;AAAA,IAEA,EAAE,mBAAmB,WAAW,WAAW,gBAAgB,cACvD,EAAE,WAAW,YAAY,SAAS,cAAc;AAAA,IAAI;AACpD;AAAA,EACJ;AACA,QAAM,SAAS,eAAgB,cAAc,MAAM;AAE/C,UAAM,KAAK,KAAK,YAAY,WAAW,UAAU,cAAc,UAAU;AACzE,QAAIC,UAAS,GAAG;AAChB,QAAI;AACA,MAAAA,UAASA,QAAO,MAAM,KAAK,MAAM,CAAC;AAMtC,YAAQ,MAAM,QAAQ,IAAI;AAAA,MACtBA,QAAO,cAAc,EAAE,GAAG,IAAI;AAAA,MAC9B,WAAW,GAAG;AAAA,IAClB,CAAC,GAAG,CAAC;AAAA,EACT;AACA,gBAAc,IAAI,MAAM,MAAM;AAC9B,SAAO;AACX;AACA,aAAa,CAAC,cAAc;AAAA,EACxB,GAAG;AAAA,EACH,KAAK,CAAC,QAAQ,MAAM,aAAa,UAAU,QAAQ,IAAI,KAAK,SAAS,IAAI,QAAQ,MAAM,QAAQ;AAAA,EAC/F,KAAK,CAAC,QAAQ,SAAS,CAAC,CAAC,UAAU,QAAQ,IAAI,KAAK,SAAS,IAAI,QAAQ,IAAI;AACjF,EAAE;;;ICnEW,kCAAyB;EACpC,YAA6B,WAA6B;AAA7B,SAAS,YAAT;;;;EAG7B,wBAAqB;AACnB,UAAM,YAAY,KAAK,UAAU,aAAY;AAG7C,WAAO,UACJ,IAAI,cAAW;AACd,UAAI,yBAAyB,QAAQ,GAAG;AACtC,cAAM,UAAU,SAAS,aAAY;AACrC,eAAO,GAAG,QAAQ,OAAO,IAAI,QAAQ,OAAO;aACvC;AACL,eAAO;;IAEX,CAAC,EACA,OAAO,eAAa,SAAS,EAC7B,KAAK,GAAG;;AAEd;AASD,SAAS,yBAAyB,UAAwB;AACxD,QAAM,YAAY,SAAS,aAAY;AACvC,UAAO,uCAAW,UAAI;AACxB;;;ACtCO,IAAM,SAAS,IAAI,OAAO,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+BzC,IAAMC,sBAAqB;AAE3B,IAAM,sBAAsB;EACjC,CAACC,MAAO,GAAG;EACX,CAACC,MAAa,GAAG;EACjB,CAACC,MAAa,GAAG;EACjB,CAACC,MAAmB,GAAG;EACvB,CAACC,MAAY,GAAG;EAChB,CAACC,MAAkB,GAAG;EACtB,CAACC,MAAQ,GAAG;EACZ,CAACC,MAAc,GAAG;EAClB,CAACC,MAAY,GAAG;EAChB,CAACC,MAAe,GAAG;EACnB,CAACC,MAAkB,GAAG;EACtB,CAACC,MAAa,GAAG;EACjB,CAACC,MAAmB,GAAG;EACvB,CAACC,MAAiB,GAAG;EACrB,CAACC,MAAuB,GAAG;EAC3B,CAACC,MAAa,GAAG;EACjB,CAACC,MAAmB,GAAG;EACvB,CAACC,MAAe,GAAG;EACnB,CAACC,MAAqB,GAAG;EACzB,CAACC,MAAgB,GAAG;EACpB,CAACC,MAAsB,GAAG;EAC1B,CAACC,MAAW,GAAG;EACf,CAACC,MAAiB,GAAG;EACrB,CAACC,MAAa,GAAG;EACjB,CAACC,MAAmB,GAAG;EACvB,CAACC,MAAM,GAAG;EACV,WAAW;;EACX,CAACC,IAAW,GAAG;;AC/CJ,IAAA,QAAQ,oBAAI,IAAG;AAKf,IAAA,cAAc,oBAAI,IAAG;AAQrB,IAAA,cAAc,oBAAI,IAAG;AAOlB,SAAA,cACd,KACA,WAAuB;AAEvB,MAAI;AACD,QAAwB,UAAU,aAAa,SAAS;WAClD,GAAG;AACV,WAAO,MACL,aAAa,UAAU,IAAI,wCAAwC,IAAI,IAAI,IAC3E,CAAC;;AAGP;AAMgB,SAAA,yBACd,KACA,WAAoB;AAEnB,MAAwB,UAAU,wBAAwB,SAAS;AACtE;AASM,SAAU,mBACd,WAAuB;AAEvB,QAAM,gBAAgB,UAAU;AAChC,MAAI,YAAY,IAAI,aAAa,GAAG;AAClC,WAAO,MACL,sDAAsD,aAAa,GAAG;AAGxE,WAAO;;AAGT,cAAY,IAAI,eAAe,SAAS;AAGxC,aAAW,OAAO,MAAM,OAAM,GAAI;AAChC,kBAAc,KAAwB,SAAS;;AAGjD,aAAW,aAAa,YAAY,OAAM,GAAI;AAC5C,kBAAc,WAAoC,SAAS;;AAG7D,SAAO;AACT;AAWgB,SAAA,aACd,KACAC,OAAO;AAEP,QAAM,sBAAuB,IAAwB,UAClD,YAAY,WAAW,EACvB,aAAa,EAAE,UAAU,KAAI,CAAE;AAClC,MAAI,qBAAqB;AACvB,SAAK,oBAAoB,iBAAgB;;AAE3C,SAAQ,IAAwB,UAAU,YAAYA,KAAI;AAC5D;AAUM,SAAU,uBACd,KACAA,OACA,qBAA6B5B,qBAAkB;AAE/C,eAAa,KAAK4B,KAAI,EAAE,cAAc,kBAAkB;AAC1D;AAUM,SAAU,eACd,KAAwD;AAExD,SAAQ,IAAoB,YAAY;AAC1C;AAUM,SAAU,6BACd,KAAwD;AAExD,MAAI,eAAe,GAAG,GAAG;AACvB,WAAO;;AAET,SACE,iBAAiB,OACjB,mBAAmB,OACnB,oBAAoB,OACpB,oCAAoC;AAExC;AAUM,SAAU,qBACd,KAAuD;AAEvD,MAAI,QAAQ,QAAQ,QAAQ,QAAW;AACrC,WAAO;;AAET,SAAQ,IAA0B,aAAa;AACjD;SAOgB,mBAAgB;AAC9B,cAAY,MAAK;AACnB;AC7KA,IAAM,SAA6B;EACjC;IAAA;;EAAA,GACE;EAEF;IAAA;;EAAA,GAAyB;EACzB;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GAAwB;EACxB;IAAA;;EAAA,GAA+B;EAC/B;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EAEF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;EACF;IAAA;;EAAA,GACE;;AAgBG,IAAM,gBAAgB,IAAI,aAC/B,OACA,YACA,MAAM;ICrDK,wBAAe;EAc1B,YACE,SACA,QACA,WAA6B;AANrB,SAAU,aAAG;AAQrB,SAAK,WAAW,EAAE,GAAG,QAAO;AAC5B,SAAK,UAAU,EAAE,GAAG,OAAM;AAC1B,SAAK,QAAQ,OAAO;AACpB,SAAK,kCACH,OAAO;AACT,SAAK,aAAa;AAClB,SAAK,UAAU,aACb,IAAI;MAAU;MAAO,MAAM;MAAI;;IAAA,CAAuB;;EAI1D,IAAI,iCAA8B;AAChC,SAAK,eAAc;AACnB,WAAO,KAAK;;EAGd,IAAI,+BAA+B,KAAY;AAC7C,SAAK,eAAc;AACnB,SAAK,kCAAkC;;EAGzC,IAAI,OAAI;AACN,SAAK,eAAc;AACnB,WAAO,KAAK;;EAGd,IAAI,UAAO;AACT,SAAK,eAAc;AACnB,WAAO,KAAK;;EAGd,IAAI,SAAM;AACR,SAAK,eAAc;AACnB,WAAO,KAAK;;EAGd,IAAI,YAAS;AACX,WAAO,KAAK;;EAGd,IAAI,YAAS;AACX,WAAO,KAAK;;EAGd,IAAI,UAAU,KAAY;AACxB,SAAK,aAAa;;;;;;EAOV,iBAAc;AACtB,QAAI,KAAK,WAAW;AAClB,YAAM,cAAc,OAAM,eAAuB,EAAE,SAAS,KAAK,MAAK,CAAE;;;AAG7E;ACxED,SAAS,iBAAiB,aAAqB,WAAiB;AAC9D,QAAM,aAAa,aAAa,YAAY,MAAM,GAAG,EAAE,CAAC,CAAC;AACzD,MAAI,eAAe,MAAM;AACvB,YAAQ,MACN,qBAAqB,SAAS,+CAA+C;AAE/E;;AAEF,QAAM,WAAW,KAAK,MAAM,UAAU,EAAE;AACxC,MAAI,aAAa,QAAW;AAC1B,YAAQ,MACN,qBAAqB,SAAS,mDAAmD;AAEnF;;AAEF,QAAM,MAAM,KAAK,MAAM,UAAU,EAAE,MAAM;AACzC,QAAM,OAAM,oBAAI,KAAI,GAAG,QAAO;AAC9B,QAAM,OAAO,MAAM;AACnB,MAAI,QAAQ,GAAG;AACb,YAAQ,MACN,qBAAqB,SAAS,qCAAqC;;AAGzE;AAEM,IAAO,wBAAP,cACI,gBAAe;EAOvB,YACE,SACA,cACAA,OACA,WAA6B;AAG7B,UAAM,iCACJ,aAAa,mCAAmC,SAC5C,aAAa,iCACb;AAGN,UAAM,SAAwC;MAC5C,MAAAA;MACA;;AAGF,QAAK,QAA4B,WAAW,QAAW;AAErD,YAAM,SAA4B,QAAQ,SAAS;WAC9C;AACL,YAAM,UAA2B;AACjC,YAAM,QAAQ,SAAS,QAAQ,SAAS;;AAI1C,SAAK,gBAAgB;MACnB;MACA,GAAG;;AAIL,QAAI,KAAK,cAAc,aAAa;AAClC,uBAAiB,KAAK,cAAc,aAAa,aAAa;;AAIhE,QAAI,KAAK,cAAc,eAAe;AACpC,uBAAiB,KAAK,cAAc,eAAe,eAAe;;AAGpE,SAAK,wBAAwB;AAC7B,QAAI,OAAO,yBAAyB,aAAa;AAC/C,WAAK,wBAAwB,IAAI,qBAAqB,MAAK;AACzD,aAAK,iBAAgB;MACvB,CAAC;;AAGH,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK,cAAc,cAAc;AAIlD,SAAK,cAAc,iBAAiB;AACpC,iBAAa,iBAAiB;AAE9B,oBAAgBD,QAAaE,WAAS,WAAW;;EAGnD,SAAM;AACJ,WAAO;;EAGT,IAAI,WAAQ;AACV,WAAO,KAAK;;;;EAKd,YAAY,KAAuB;AACjC,QAAI,KAAK,WAAW;AAClB;;AAEF,SAAK;AACL,QAAI,QAAQ,UAAa,KAAK,0BAA0B,MAAM;AAC5D,WAAK,sBAAsB,SAAS,KAAK,IAAI;;;;EAKjD,cAAW;AACT,QAAI,KAAK,WAAW;AAClB,aAAO;;AAET,WAAO,EAAE,KAAK;;;;;EAMR,mBAAgB;AACtB,SAAK,UAAU,IAAI;;EAGrB,IAAI,WAAQ;AACV,SAAK,eAAc;AACnB,WAAO,KAAK;;;;;;EAOJ,iBAAc;AACtB,QAAI,KAAK,WAAW;AAClB,YAAM,cAAc;QAAM;;MAAA;;;AAG/B;AC9GM,IAAM,cAAc;SA2EX,cACd,UACA,YAAY,CAAA,GAAE;AAEd,MAAI,UAAU;AAEd,MAAI,OAAO,cAAc,UAAU;AACjC,UAAMD,QAAO;AACb,gBAAY,EAAE,MAAAA,MAAI;;AAGpB,QAAM,SAAwC;IAC5C,MAAM5B;IACN,gCAAgC;IAChC,GAAG;;AAEL,QAAM4B,QAAO,OAAO;AAEpB,MAAI,OAAOA,UAAS,YAAY,CAACA,OAAM;AACrC,UAAM,cAAc,OAA8B,gBAAA;MAChD,SAAS,OAAOA,KAAI;IACrB,CAAA;;AAGH,cAAA,UAAY,oBAAmB;AAE/B,MAAI,CAAC,SAAS;AACZ,UAAM,cAAc;MAAM;;IAAA;;AAG5B,QAAM,cAAc,MAAM,IAAIA,KAAI;AAClC,MAAI,aAAa;AAEf,QACE,UAAU,SAAS,YAAY,OAAO,KACtC,UAAU,QAAQ,YAAY,MAAM,GACpC;AACA,aAAO;WACF;AACL,YAAM,cAAc,OAA+B,iBAAA,EAAE,SAASA,MAAI,CAAE;;;AAIxE,QAAM,YAAY,IAAI,mBAAmBA,KAAI;AAC7C,aAAW,aAAa,YAAY,OAAM,GAAI;AAC5C,cAAU,aAAa,SAAS;;AAGlC,QAAM,SAAS,IAAI,gBAAgB,SAAS,QAAQ,SAAS;AAE7D,QAAM,IAAIA,OAAM,MAAM;AAEtB,SAAO;AACT;SAuEgB,oBACd,UACA,mBAA8C,CAAA,GAAE;AAEhD,MAAI,UAAS,KAAM,CAAC,YAAW,GAAI;AAEjC,UAAM,cAAc;MAAM;;IAAA;;AAG5B,MAAI;AACJ,MAAI,oBAA+C,oBAAoB,CAAA;AAEvE,MAAI,UAAU;AACZ,QAAI,eAAe,QAAQ,GAAG;AAC5B,wBAAkB,SAAS;eAClB,6BAA6B,QAAQ,GAAG;AACjD,0BAAoB;WACf;AACL,wBAAkB;;;AAItB,MAAI,kBAAkB,mCAAmC,QAAW;AAClE,sBAAkB,iCAAiC;;AAGrD,sBAAA,kBAAoB,oBAAmB;AACvC,MAAI,CAAC,iBAAiB;AACpB,UAAM,cAAc;MAAM;;IAAA;;AAI5B,QAAM,UAAU;IACd,GAAG;IACH,GAAG;;AAKL,MAAI,QAAQ,mBAAmB,QAAW;AACxC,WAAO,QAAQ;;AAGjB,QAAM,WAAW,CAAC,MAAqB;AACrC,WAAO,CAAC,GAAG,CAAC,EAAE,OACZ,CAAC,MAAM,MAAO,KAAK,KAAK,IAAI,IAAI,IAAI,EAAE,WAAW,CAAC,IAAK,GACvD,CAAC;EAEL;AAEA,MAAI,kBAAkB,mBAAmB,QAAW;AAClD,QAAI,OAAO,yBAAyB,aAAa;AAC/C,YAAM,cAAc,OAElB,uCAAA,CAAA,CAAE;;;AAKR,QAAM,aAAa,KAAK,SAAS,KAAK,UAAU,OAAO,CAAC;AACxD,QAAM,cAAc,YAAY,IAAI,UAAU;AAC9C,MAAI,aAAa;AACd,gBAAsC,YACrC,kBAAkB,cAAc;AAElC,WAAO;;AAGT,QAAM,YAAY,IAAI,mBAAmB,UAAU;AACnD,aAAW,aAAa,YAAY,OAAM,GAAI;AAC5C,cAAU,aAAa,SAAS;;AAGlC,QAAM,SAAS,IAAI,sBACjB,iBACA,mBACA,YACA,SAAS;AAGX,cAAY,IAAI,YAAY,MAAM;AAElC,SAAO;AACT;AA+BgB,SAAA,OAAOA,QAAe5B,qBAAkB;AACtD,QAAM,MAAM,MAAM,IAAI4B,KAAI;AAC1B,MAAI,CAAC,OAAOA,UAAS5B,uBAAsB,oBAAmB,GAAI;AAChE,WAAO,cAAa;;AAEtB,MAAI,CAAC,KAAK;AACR,UAAM,cAAc,OAAwB,UAAA,EAAE,SAAS4B,MAAI,CAAE;;AAG/D,SAAO;AACT;SAMgB,UAAO;AACrB,SAAO,MAAM,KAAK,MAAM,OAAM,CAAE;AAClC;AAmBO,eAAe,UAAU,KAAgB;AAC9C,MAAI,mBAAmB;AACvB,QAAMA,QAAO,IAAI;AACjB,MAAI,MAAM,IAAIA,KAAI,GAAG;AACnB,uBAAmB;AACnB,UAAM,OAAOA,KAAI;aACR,YAAY,IAAIA,KAAI,GAAG;AAChC,UAAM,oBAAoB;AAC1B,QAAI,kBAAkB,YAAW,KAAM,GAAG;AACxC,kBAAY,OAAOA,KAAI;AACvB,yBAAmB;;;AAIvB,MAAI,kBAAkB;AACpB,UAAM,QAAQ,IACX,IAAwB,UACtB,aAAY,EACZ,IAAI,cAAY,SAAS,OAAM,CAAE,CAAC;AAEtC,QAAwB,YAAY;;AAEzC;SAUgB,gBACd,kBACAC,UACA,SAAgB;AAIhB,MAAI,UAAU,oBAAoB,gBAAgB,KAAK;AACvD,MAAI,SAAS;AACX,eAAW,IAAI,OAAO;;AAExB,QAAM,kBAAkB,QAAQ,MAAM,OAAO;AAC7C,QAAM,kBAAkBA,SAAQ,MAAM,OAAO;AAC7C,MAAI,mBAAmB,iBAAiB;AACtC,UAAM,UAAU;MACd,+BAA+B,OAAO,mBAAmBA,QAAO;;AAElE,QAAI,iBAAiB;AACnB,cAAQ,KACN,iBAAiB,OAAO,mDAAmD;;AAG/E,QAAI,mBAAmB,iBAAiB;AACtC,cAAQ,KAAK,KAAK;;AAEpB,QAAI,iBAAiB;AACnB,cAAQ,KACN,iBAAiBA,QAAO,mDAAmD;;AAG/E,WAAO,KAAK,QAAQ,KAAK,GAAG,CAAC;AAC7B;;AAEF,qBACE,IAAI;IACF,GAAG,OAAO;IACV,OAAO,EAAE,SAAS,SAAAA,SAAO;IAAG;;EAAA,CAE7B;AAEL;AASgB,SAAA,MACd,aACA,SAAoB;AAEpB,MAAI,gBAAgB,QAAQ,OAAO,gBAAgB,YAAY;AAC7D,UAAM,cAAc;MAAM;;IAAA;;AAE5B,oBAAkB,aAAa,OAAO;AACxC;AAWM,SAAUC,aAAY,UAAwB;AAClDC,cAAgB,QAAQ;AAC1B;AC7eA,IAAM,UAAU;AAChB,IAAM,aAAa;AACnB,IAAM,aAAa;AASnB,IAAI,YAAiD;AACrD,SAAS,eAAY;AACnB,MAAI,CAAC,WAAW;AACd,gBAAY,OAAc,SAAS,YAAY;MAC7C,SAAS,CAAC,IAAI,eAAc;AAM1B,gBAAQ,YAAU;UAChB,KAAK;AACH,gBAAI;AACF,iBAAG,kBAAkB,UAAU;qBACxB,GAAG;AAIV,sBAAQ,KAAK,CAAC;;;;IAIvB,CAAA,EAAE,MAAM,OAAI;AACX,YAAM,cAAc,OAA0B,YAAA;QAC5C,sBAAsB,EAAE;MACzB,CAAA;IACH,CAAC;;AAEH,SAAO;AACT;AAEO,eAAe,4BACpB,KAAgB;AAEhB,MAAI;AACF,UAAM,KAAK,MAAM,aAAY;AAC7B,UAAM,KAAK,GAAG,YAAY,UAAU;AACpC,UAAM,SAAS,MAAM,GAAG,YAAY,UAAU,EAAE,IAAI,WAAW,GAAG,CAAC;AAGnE,UAAM,GAAG;AACT,WAAO;WACA,GAAG;AACV,QAAI,aAAa,eAAe;AAC9B,aAAO,KAAK,EAAE,OAAO;WAChB;AACL,YAAM,cAAc,cAAc,OAAyB,WAAA;QACzD,sBAAuB,uBAAa;MACrC,CAAA;AACD,aAAO,KAAK,YAAY,OAAO;;;AAGrC;AAEO,eAAe,2BACpB,KACA,iBAAsC;AAEtC,MAAI;AACF,UAAM,KAAK,MAAM,aAAY;AAC7B,UAAM,KAAK,GAAG,YAAY,YAAY,WAAW;AACjD,UAAM,cAAc,GAAG,YAAY,UAAU;AAC7C,UAAM,YAAY,IAAI,iBAAiB,WAAW,GAAG,CAAC;AACtD,UAAM,GAAG;WACF,GAAG;AACV,QAAI,aAAa,eAAe;AAC9B,aAAO,KAAK,EAAE,OAAO;WAChB;AACL,YAAM,cAAc,cAAc,OAA2B,WAAA;QAC3D,sBAAuB,uBAAa;MACrC,CAAA;AACD,aAAO,KAAK,YAAY,OAAO;;;AAGrC;AAEA,SAAS,WAAW,KAAgB;AAClC,SAAO,GAAG,IAAI,IAAI,IAAI,IAAI,QAAQ,KAAK;AACzC;AC5EA,IAAM,mBAAmB;AAClB,IAAM,4BAA4B;IAE5B,6BAAoB;EAyB/B,YAA6B,WAA6B;AAA7B,SAAS,YAAT;AAT7B,SAAgB,mBAAiC;AAU/C,UAAM,MAAM,KAAK,UAAU,YAAY,KAAK,EAAE,aAAY;AAC1D,SAAK,WAAW,IAAI,qBAAqB,GAAG;AAC5C,SAAK,0BAA0B,KAAK,SAAS,KAAI,EAAG,KAAK,YAAS;AAChE,WAAK,mBAAmB;AACxB,aAAO;IACT,CAAC;;;;;;;;;EAUH,MAAM,mBAAgB;;AACpB,QAAI;AACF,YAAM,iBAAiB,KAAK,UACzB,YAAY,iBAAiB,EAC7B,aAAY;AAIf,YAAM,QAAQ,eAAe,sBAAqB;AAClD,YAAM,OAAO,iBAAgB;AAC7B,YAAI,UAAK,qBAAL,mBAAuB,eAAc,MAAM;AAC7C,aAAK,mBAAmB,MAAM,KAAK;AAEnC,cAAI,UAAK,qBAAL,mBAAuB,eAAc,MAAM;AAC7C;;;AAKJ,UACE,KAAK,iBAAiB,0BAA0B,QAChD,KAAK,iBAAiB,WAAW,KAC/B,yBAAuB,oBAAoB,SAAS,IAAI,GAE1D;AACA;aACK;AAEL,aAAK,iBAAiB,WAAW,KAAK,EAAE,MAAM,MAAK,CAAE;AAIrD,YACE,KAAK,iBAAiB,WAAW,SAAS,2BAC1C;AACA,gBAAM,uBAAuB,wBAC3B,KAAK,iBAAiB,UAAU;AAElC,eAAK,iBAAiB,WAAW,OAAO,sBAAsB,CAAC;;;AAInE,aAAO,KAAK,SAAS,UAAU,KAAK,gBAAgB;aAC7C,GAAG;AACV,aAAO,KAAK,CAAC;;;;;;;;;;EAWjB,MAAM,sBAAmB;;AACvB,QAAI;AACF,UAAI,KAAK,qBAAqB,MAAM;AAClC,cAAM,KAAK;;AAGb,YACE,UAAK,qBAAL,mBAAuB,eAAc,QACrC,KAAK,iBAAiB,WAAW,WAAW,GAC5C;AACA,eAAO;;AAET,YAAM,OAAO,iBAAgB;AAE7B,YAAM,EAAE,kBAAkB,cAAa,IAAK,2BAC1C,KAAK,iBAAiB,UAAU;AAElC,YAAM,eAAe,8BACnB,KAAK,UAAU,EAAE,SAAS,GAAG,YAAY,iBAAgB,CAAE,CAAC;AAG9D,WAAK,iBAAiB,wBAAwB;AAC9C,UAAI,cAAc,SAAS,GAAG;AAE5B,aAAK,iBAAiB,aAAa;AAInC,cAAM,KAAK,SAAS,UAAU,KAAK,gBAAgB;aAC9C;AACL,aAAK,iBAAiB,aAAa,CAAA;AAEnC,aAAK,KAAK,SAAS,UAAU,KAAK,gBAAgB;;AAEpD,aAAO;aACA,GAAG;AACV,aAAO,KAAK,CAAC;AACb,aAAO;;;AAGZ;AAED,SAAS,mBAAgB;AACvB,QAAM,QAAQ,oBAAI,KAAI;AAEtB,SAAO,MAAM,YAAW,EAAG,UAAU,GAAG,EAAE;AAC5C;SAEgB,2BACd,iBACA,UAAU,kBAAgB;AAO1B,QAAM,mBAA4C,CAAA;AAElD,MAAI,gBAAgB,gBAAgB,MAAK;AACzC,aAAW,uBAAuB,iBAAiB;AAEjD,UAAM,iBAAiB,iBAAiB,KACtC,QAAM,GAAG,UAAU,oBAAoB,KAAK;AAE9C,QAAI,CAAC,gBAAgB;AAEnB,uBAAiB,KAAK;QACpB,OAAO,oBAAoB;QAC3B,OAAO,CAAC,oBAAoB,IAAI;MACjC,CAAA;AACD,UAAI,WAAW,gBAAgB,IAAI,SAAS;AAG1C,yBAAiB,IAAG;AACpB;;WAEG;AACL,qBAAe,MAAM,KAAK,oBAAoB,IAAI;AAGlD,UAAI,WAAW,gBAAgB,IAAI,SAAS;AAC1C,uBAAe,MAAM,IAAG;AACxB;;;AAKJ,oBAAgB,cAAc,MAAM,CAAC;;AAEvC,SAAO;IACL;IACA;;AAEJ;IAEa,6BAAoB;EAE/B,YAAmB,KAAgB;AAAhB,SAAG,MAAH;AACjB,SAAK,0BAA0B,KAAK,6BAA4B;;EAElE,MAAM,+BAA4B;AAChC,QAAI,CAAC,qBAAoB,GAAI;AAC3B,aAAO;WACF;AACL,aAAO,0BAAyB,EAC7B,KAAK,MAAM,IAAI,EACf,MAAM,MAAM,KAAK;;;;;;EAMxB,MAAM,OAAI;AACR,UAAM,kBAAkB,MAAM,KAAK;AACnC,QAAI,CAAC,iBAAiB;AACpB,aAAO,EAAE,YAAY,CAAA,EAAE;WAClB;AACL,YAAM,qBAAqB,MAAM,4BAA4B,KAAK,GAAG;AACrE,UAAI,yDAAoB,YAAY;AAClC,eAAO;aACF;AACL,eAAO,EAAE,YAAY,CAAA,EAAE;;;;;EAK7B,MAAM,UAAU,kBAAuC;AACrD,UAAM,kBAAkB,MAAM,KAAK;AACnC,QAAI,CAAC,iBAAiB;AACpB;WACK;AACL,YAAM,2BAA2B,MAAM,KAAK,KAAI;AAChD,aAAO,2BAA2B,KAAK,KAAK;QAC1C,uBACE,iBAAiB,yBACjB,yBAAyB;QAC3B,YAAY,iBAAiB;MAC9B,CAAA;;;;EAIL,MAAM,IAAI,kBAAuC;AAC/C,UAAM,kBAAkB,MAAM,KAAK;AACnC,QAAI,CAAC,iBAAiB;AACpB;WACK;AACL,YAAM,2BAA2B,MAAM,KAAK,KAAI;AAChD,aAAO,2BAA2B,KAAK,KAAK;QAC1C,uBACE,iBAAiB,yBACjB,yBAAyB;QAC3B,YAAY;UACV,GAAG,yBAAyB;UAC5B,GAAG,iBAAiB;QACrB;MACF,CAAA;;;AAGN;AAOK,SAAU,WAAW,iBAAwC;AAEjE,SAAO;;IAEL,KAAK,UAAU,EAAE,SAAS,GAAG,YAAY,gBAAe,CAAE;EAAC,EAC3D;AACJ;AAMM,SAAU,wBACd,YAAiC;AAEjC,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO;;AAGT,MAAI,uBAAuB;AAC3B,MAAI,wBAAwB,WAAW,CAAC,EAAE;AAE1C,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,QAAI,WAAW,CAAC,EAAE,OAAO,uBAAuB;AAC9C,8BAAwB,WAAW,CAAC,EAAE;AACtC,6BAAuB;;;AAI3B,SAAO;AACT;ACpTM,SAAU,uBAAuB,SAAgB;AACrD,qBACE,IAAI;IACF;IACA,eAAa,IAAI,0BAA0B,SAAS;IAAC;;EAAA,CAEtD;AAEH,qBACE,IAAI;IACF;IACA,eAAa,IAAI,qBAAqB,SAAS;IAAC;;EAAA,CAEjD;AAIH,kBAAgBH,QAAMC,WAAS,OAAO;AAEtC,kBAAgBD,QAAMC,WAAS,SAAkB;AAEjD,kBAAgB,WAAW,EAAE;AAC/B;AChBA,uBAAuB,EAAiB;", "names": ["stringToByteArray", "name", "querystring", "name", "LogLevel", "name", "instance", "name", "version", "target", "DEFAULT_ENTRY_NAME", "appName", "appCompatName", "analyticsName", "analyticsCompatName", "appCheckName", "appCheckCompatName", "authName", "authCompatName", "databaseName", "dataconnectName", "databaseCompatName", "functionsName", "functionsCompatName", "installationsName", "installationsCompatName", "messagingName", "messagingCompatName", "performanceName", "performanceCompatName", "remoteConfigName", "remoteConfigCompatName", "storageName", "storageCompatName", "firestoreName", "firestoreCompatName", "aiName", "packageName", "name", "version", "setLogLevel", "setLogLevelImpl"]}